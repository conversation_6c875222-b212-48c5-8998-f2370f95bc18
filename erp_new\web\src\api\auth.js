import request from './request'

/**
 * 用户登录
 * @param {string} username 用户名
 * @param {string} password 密码
 * @param {boolean} rememberMe 记住密码
 * @returns {Promise}
 */
export function login(username, password, rememberMe = false) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: {
      username,
      password,
      rememberMe
    }
  })
}

/**
 * 用户登出
 * @returns {Promise}
 */
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

/**
 * 获取用户信息
 * @returns {Promise}
 */
export function getUserInfo() {
  return request({
    url: '/auth/user_info',
    method: 'get'
  })
}

/**
 * 刷新令牌
 * @returns {Promise}
 */
export function refreshToken() {
  return request({
    url: '/auth/refresh_token',
    method: 'post'
  })
}

/**
 * 验证令牌
 * @returns {Promise}
 */
export function verifyToken() {
  return request({
    url: '/auth/verify_token',
    method: 'post'
  })
}

/**
 * 修改密码
 * @param {string} oldPassword 旧密码
 * @param {string} newPassword 新密码
 * @param {string} confirmPassword 确认密码
 * @returns {Promise}
 */
export function changePassword(oldPassword, newPassword, confirmPassword) {
  return request({
    url: '/auth/change_password',
    method: 'post',
    data: {
      old_password: oldPassword,
      new_password: newPassword,
      confirm_password: confirmPassword
    }
  })
}
