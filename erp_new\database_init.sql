-- ERP系统数据库初始化脚本
-- 基于 erp2025 的表结构，适配 erp_new 系统

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `erp_new` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `erp_new`;

-- 管理员表
CREATE TABLE IF NOT EXISTS `admin` (
  `uid` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `userpwd` varchar(32) NOT NULL COMMENT '密码(MD5)',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `roleid` int(11) DEFAULT 0 COMMENT '角色ID，0为超级管理员',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifyTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_roleid` (`roleid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 插入默认管理员账号
INSERT INTO `admin` (`username`, `userpwd`, `name`, `mobile`, `roleid`, `status`) VALUES
('admin', '202cb962ac59075b964b07152d234b70', '系统管理员', '13800138000', 0, 1)
ON DUPLICATE KEY UPDATE `userpwd` = VALUES(`userpwd`);

-- 访问令牌表
CREATE TABLE IF NOT EXISTS `tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `token` varchar(64) NOT NULL COMMENT '令牌',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text DEFAULT NULL COMMENT '用户代理',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `token` (`token`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`user_id`) REFERENCES `admin` (`uid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问令牌表';

-- 角色表
CREATE TABLE IF NOT EXISTS `role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `lever` text DEFAULT NULL COMMENT '权限列表，逗号分隔',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifyTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 插入默认角色
INSERT INTO `role` (`name`, `lever`, `description`) VALUES
('普通用户', '1,2,3,4,5', '普通用户角色'),
('财务人员', '1,2,3,4,5,6,7,8,9,10', '财务人员角色'),
('仓库管理员', '1,2,3,4,5,11,12,13,14,15', '仓库管理员角色')
ON DUPLICATE KEY UPDATE `lever` = VALUES(`lever`);

-- 操作日志表
CREATE TABLE IF NOT EXISTS `log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `name` varchar(50) DEFAULT NULL COMMENT '用户名',
  `operation` varchar(100) NOT NULL COMMENT '操作类型',
  `content` text DEFAULT NULL COMMENT '操作内容',
  `ip` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 商品分类表
CREATE TABLE IF NOT EXISTS `category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `parentId` int(11) DEFAULT 0 COMMENT '父分类ID',
  `level` tinyint(2) DEFAULT 1 COMMENT '分类级别',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifyTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parentId`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';

-- 计量单位表
CREATE TABLE IF NOT EXISTS `unit` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '单位ID',
  `name` varchar(50) NOT NULL COMMENT '单位名称',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计量单位表';

-- 插入默认单位
INSERT INTO `unit` (`name`) VALUES
('个'), ('件'), ('箱'), ('包'), ('袋'), ('瓶'), ('盒'), ('套'), ('台'), ('张'),
('米'), ('千克'), ('克'), ('吨'), ('升'), ('毫升'), ('平方米'), ('立方米')
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- 仓库表
CREATE TABLE IF NOT EXISTS `storage` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '仓库ID',
  `name` varchar(100) NOT NULL COMMENT '仓库名称',
  `address` varchar(255) DEFAULT NULL COMMENT '仓库地址',
  `manager` varchar(50) DEFAULT NULL COMMENT '仓库管理员',
  `mobile` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifyTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库表';

-- 插入默认仓库
INSERT INTO `storage` (`name`, `address`, `manager`) VALUES
('主仓库', '总部仓库', '仓库管理员'),
('分仓库', '分部仓库', '分仓管理员')
ON DUPLICATE KEY UPDATE `name` = VALUES(`name`);

-- 商品表
CREATE TABLE IF NOT EXISTS `inventory` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `name` varchar(200) NOT NULL COMMENT '商品名称',
  `number` varchar(100) DEFAULT NULL COMMENT '商品编号',
  `barCode` varchar(100) DEFAULT NULL COMMENT '条形码',
  `spec` varchar(200) DEFAULT NULL COMMENT '规格型号',
  `categoryId` int(11) DEFAULT NULL COMMENT '分类ID',
  `categoryName` varchar(100) DEFAULT NULL COMMENT '分类名称',
  `unitId` int(11) DEFAULT NULL COMMENT '单位ID',
  `unitName` varchar(50) DEFAULT NULL COMMENT '单位名称',
  `purchasePrice` decimal(10,2) DEFAULT 0.00 COMMENT '采购价格',
  `salePrice` decimal(10,2) DEFAULT 0.00 COMMENT '销售价格',
  `lowQty` decimal(10,2) DEFAULT 0.00 COMMENT '最低库存',
  `highQty` decimal(10,2) DEFAULT 0.00 COMMENT '最高库存',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifyTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `number` (`number`),
  KEY `idx_category_id` (`categoryId`),
  KEY `idx_status` (`status`),
  KEY `idx_barcode` (`barCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 客户表
CREATE TABLE IF NOT EXISTS `contact` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(100) NOT NULL COMMENT '客户名称',
  `number` varchar(50) DEFAULT NULL COMMENT '客户编号',
  `type` tinyint(1) DEFAULT 1 COMMENT '类型：1客户，2供应商，3客户+供应商',
  `mobile` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `contact` varchar(50) DEFAULT NULL COMMENT '联系人',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `isDelete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0否，1是',
  `createTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifyTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户供应商表';

-- 库存明细表
CREATE TABLE IF NOT EXISTS `inventory_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `invId` int(11) NOT NULL COMMENT '商品ID',
  `locationId` int(11) NOT NULL COMMENT '仓库ID',
  `qty` decimal(10,2) DEFAULT 0.00 COMMENT '库存数量',
  `price` decimal(10,2) DEFAULT 0.00 COMMENT '成本价格',
  `updateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `inv_location` (`invId`, `locationId`),
  KEY `idx_inv_id` (`invId`),
  KEY `idx_location_id` (`locationId`),
  FOREIGN KEY (`invId`) REFERENCES `inventory` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`locationId`) REFERENCES `storage` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存明细表';
