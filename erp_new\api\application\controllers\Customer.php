<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 客户管理控制器
 * 实现客户相关的API接口
 */
class Customer extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('customer_model');
        $this->load->model('category_model');
    }

    /**
     * 获取客户列表
     * GET /api/customers
     */
    public function index_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? $this->get('page', true) : 1,
            'limit' => $this->get('limit', true) ? $this->get('limit', true) : 10,
            'keyword' => $this->get('keyword', true),
            'category_id' => $this->get('category_id', true),
            'status' => $this->get('status', true)
        ];

        // 获取客户列表和总数
        $customers = $this->customer_model->get_customers($params);
        $total = $this->customer_model->count_customers($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $customers,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取单个客户详情
     * GET /api/customers/:id
     */
    public function detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '客户ID不能为空'
            ], 400);
            return;
        }

        $customer = $this->customer_model->get_customer_by_id($id);
        if (!$customer) {
            $this->response([
                'status' => false,
                'message' => '客户不存在'
            ], 404);
            return;
        }

        $this->response([
            'status' => true,
            'data' => $customer
        ], 200);
    }

    /**
     * 创建客户
     * POST /api/customers
     */
    public function create_post() {
        // 获取请求数据
        $data = [
            'name' => $this->post('name', true),
            'code' => $this->post('code', true),
            'category_id' => $this->post('category_id', true),
            'contact_name' => $this->post('contact_name', true),
            'contact_phone' => $this->post('contact_phone', true),
            'contact_email' => $this->post('contact_email', true),
            'address' => $this->post('address', true),
            'tax_number' => $this->post('tax_number', true),
            'bank_name' => $this->post('bank_name', true),
            'bank_account' => $this->post('bank_account', true),
            'credit_limit' => $this->post('credit_limit', true),
            'status' => $this->post('status', true) ? $this->post('status', true) : 'active',
            'remark' => $this->post('remark', true),
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$data['name']) {
            $this->response([
                'status' => false,
                'message' => '客户名称不能为空'
            ], 400);
            return;
        }

        // 验证客户编码唯一性
        if ($data['code'] && $this->customer_model->is_code_exists($data['code'])) {
            $this->response([
                'status' => false,
                'message' => '客户编码已存在'
            ], 400);
            return;
        }

        // 如果没有提供编码，则自动生成
        if (!$data['code']) {
            $data['code'] = 'C' . date('YmdHis');
        }

        // 创建客户
        $customer_id = $this->customer_model->create_customer($data);
        if (!$customer_id) {
            $this->response([
                'status' => false,
                'message' => '创建客户失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '创建客户成功',
            'data' => [
                'id' => $customer_id,
                'code' => $data['code']
            ]
        ], 201);
    }

    /**
     * 更新客户
     * PUT /api/customers/:id
     */
    public function update_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '客户ID不能为空'
            ], 400);
            return;
        }

        // 检查客户是否存在
        $customer = $this->customer_model->get_customer_by_id($id);
        if (!$customer) {
            $this->response([
                'status' => false,
                'message' => '客户不存在'
            ], 404);
            return;
        }

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'code' => $this->put('code', true),
            'category_id' => $this->put('category_id', true),
            'contact_name' => $this->put('contact_name', true),
            'contact_phone' => $this->put('contact_phone', true),
            'contact_email' => $this->put('contact_email', true),
            'address' => $this->put('address', true),
            'tax_number' => $this->put('tax_number', true),
            'bank_name' => $this->put('bank_name', true),
            'bank_account' => $this->put('bank_account', true),
            'credit_limit' => $this->put('credit_limit', true),
            'status' => $this->put('status', true),
            'remark' => $this->put('remark', true),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (isset($data['name']) && !$data['name']) {
            $this->response([
                'status' => false,
                'message' => '客户名称不能为空'
            ], 400);
            return;
        }

        // 验证客户编码唯一性
        if (isset($data['code']) && $data['code'] && $data['code'] !== $customer['code'] && $this->customer_model->is_code_exists($data['code'])) {
            $this->response([
                'status' => false,
                'message' => '客户编码已存在'
            ], 400);
            return;
        }

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 更新客户
        $result = $this->customer_model->update_customer($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新客户失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '更新客户成功'
        ], 200);
    }

    /**
     * 删除客户
     * DELETE /api/customers/:id
     */
    public function delete_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '客户ID不能为空'
            ], 400);
            return;
        }

        // 检查客户是否存在
        $customer = $this->customer_model->get_customer_by_id($id);
        if (!$customer) {
            $this->response([
                'status' => false,
                'message' => '客户不存在'
            ], 404);
            return;
        }

        // 检查客户是否被引用
        if ($this->customer_model->check_customer_in_use($id)) {
            $this->response([
                'status' => false,
                'message' => '该客户已被订单或其他记录引用，无法删除'
            ], 400);
            return;
        }

        // 删除客户（软删除）
        $result = $this->customer_model->delete_customer($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除客户失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '删除客户成功'
        ], 200);
    }

    /**
     * 获取客户分类列表
     * GET /api/customer-categories
     */
    public function categories_get() {
        $categories = $this->category_model->get_categories('customer');
        
        $this->response([
            'status' => true,
            'data' => $categories
        ], 200);
    }
}
