<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 销售模型类
 * 处理销售相关的数据库操作
 */
class Sales_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取销售订单列表
     * @param array $params 查询参数
     * @return array 销售订单列表
     */
    public function get_orders($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('o.*, c.name as customer_name, u.name as created_by_name');
        $this->db->from('sales_orders o');
        $this->db->join('customers c', 'o.customer_id = c.id', 'left');
        $this->db->join('users u', 'o.created_by = u.id', 'left');
        $this->db->where('o.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('o.order_no', $keyword);
            $this->db->or_like('c.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('o.customer_id', $params['customer_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('o.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('o.order_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('o.order_date <=', $params['end_date']);
        }
        
        $this->db->order_by('o.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取销售订单总数
     * @param array $params 查询参数
     * @return int 销售订单总数
     */
    public function count_orders($params = []) {
        $this->db->from('sales_orders o');
        $this->db->join('customers c', 'o.customer_id = c.id', 'left');
        $this->db->where('o.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('o.order_no', $keyword);
            $this->db->or_like('c.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('o.customer_id', $params['customer_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('o.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('o.order_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('o.order_date <=', $params['end_date']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取销售订单详情
     * @param int $id 销售订单ID
     * @return array|null 销售订单详情
     */
    public function get_order_by_id($id) {
        $this->db->select('o.*, c.name as customer_name, u.name as created_by_name');
        $this->db->from('sales_orders o');
        $this->db->join('customers c', 'o.customer_id = c.id', 'left');
        $this->db->join('users u', 'o.created_by = u.id', 'left');
        $this->db->where('o.id', $id);
        $this->db->where('o.is_deleted', 0);
        
        $query = $this->db->get();
        $order = $query->row_array();
        
        if ($order) {
            // 获取订单详情项
            $this->db->select('d.*, p.name as product_name, p.code as product_code, p.specification');
            $this->db->from('sales_order_details d');
            $this->db->join('products p', 'd.product_id = p.id', 'left');
            $this->db->where('d.order_id', $id);
            
            $query = $this->db->get();
            $order['details'] = $query->result_array();
        }
        
        return $order;
    }
    
    /**
     * 创建销售订单
     * @param array $data 销售订单数据
     * @param array $details 销售订单详情数据
     * @return int|bool 销售订单ID或false
     */
    public function create_order($data, $details) {
        $this->db->trans_start();
        
        // 插入销售订单
        $this->db->insert('sales_orders', $data);
        $order_id = $this->db->insert_id();
        
        // 插入销售订单详情
        if ($order_id && is_array($details) && count($details) > 0) {
            foreach ($details as $detail) {
                $detail['order_id'] = $order_id;
                $this->db->insert('sales_order_details', $detail);
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : $order_id;
    }
    
    /**
     * 更新销售订单
     * @param int $id 销售订单ID
     * @param array $data 销售订单数据
     * @param array $details 销售订单详情数据
     * @return bool 是否成功
     */
    public function update_order($id, $data, $details = null) {
        $this->db->trans_start();
        
        // 更新销售订单
        $this->db->where('id', $id);
        $this->db->update('sales_orders', $data);
        
        // 如果提供了详情数据，则更新详情
        if ($details !== null) {
            // 删除原有的详情
            $this->db->where('order_id', $id);
            $this->db->delete('sales_order_details');
            
            // 插入新的详情
            if (is_array($details) && count($details) > 0) {
                foreach ($details as $detail) {
                    $detail['order_id'] = $id;
                    $this->db->insert('sales_order_details', $detail);
                }
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 更新销售订单状态
     * @param int $id 销售订单ID
     * @param string $status 状态
     * @param int $user_id 操作用户ID
     * @return bool 是否成功
     */
    public function update_order_status($id, $status, $user_id) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($status == 'confirmed') {
            $data['confirmed_by'] = $user_id;
            $data['confirmed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'completed') {
            $data['completed_by'] = $user_id;
            $data['completed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'cancelled') {
            $data['cancelled_by'] = $user_id;
            $data['cancelled_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->where('id', $id);
        return $this->db->update('sales_orders', $data);
    }
    
    /**
     * 删除销售订单
     * @param int $id 销售订单ID
     * @return bool 是否成功
     */
    public function delete_order($id) {
        $this->db->where('id', $id);
        return $this->db->update('sales_orders', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取销售出库单列表
     * @param array $params 查询参数
     * @return array 销售出库单列表
     */
    public function get_deliveries($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('d.*, o.order_no, c.name as customer_name, w.name as warehouse_name, u.name as created_by_name');
        $this->db->from('sales_deliveries d');
        $this->db->join('sales_orders o', 'd.order_id = o.id', 'left');
        $this->db->join('customers c', 'd.customer_id = c.id', 'left');
        $this->db->join('warehouses w', 'd.warehouse_id = w.id', 'left');
        $this->db->join('users u', 'd.created_by = u.id', 'left');
        $this->db->where('d.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('d.delivery_no', $keyword);
            $this->db->or_like('o.order_no', $keyword);
            $this->db->or_like('c.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('d.customer_id', $params['customer_id']);
        }
        
        if (isset($params['warehouse_id']) && $params['warehouse_id']) {
            $this->db->where('d.warehouse_id', $params['warehouse_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('d.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('d.delivery_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('d.delivery_date <=', $params['end_date']);
        }
        
        $this->db->order_by('d.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 创建销售出库单
     * @param array $data 销售出库单数据
     * @param array $details 销售出库单详情数据
     * @return int|bool 销售出库单ID或false
     */
    public function create_delivery($data, $details) {
        $this->db->trans_start();
        
        // 插入销售出库单
        $this->db->insert('sales_deliveries', $data);
        $delivery_id = $this->db->insert_id();
        
        // 插入销售出库单详情
        if ($delivery_id && is_array($details) && count($details) > 0) {
            foreach ($details as $detail) {
                $detail['delivery_id'] = $delivery_id;
                $this->db->insert('sales_delivery_details', $detail);
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : $delivery_id;
    }
}
