<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 分类模型类
 * 处理各种分类相关的数据库操作
 */
class Category_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取分类列表
     * @param string $type 分类类型（例如：product, customer, supplier）
     * @return array 分类列表
     */
    public function get_categories($type = null) {
        $this->db->from('categories');
        $this->db->where('is_deleted', 0);
        
        if ($type !== null) {
            $this->db->where('type', $type);
        }
        
        $this->db->order_by('sort_order', 'ASC');
        $this->db->order_by('id', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 根据ID获取分类信息
     * @param int $id 分类ID
     * @return array|null 分类信息
     */
    public function get_category_by_id($id) {
        $this->db->from('categories');
        $this->db->where('id', $id);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据名称获取分类信息
     * @param string $name 分类名称
     * @param string $type 分类类型
     * @return array|null 分类信息
     */
    public function get_category_by_name($name, $type = null) {
        $this->db->from('categories');
        $this->db->where('name', $name);
        $this->db->where('is_deleted', 0);
        
        if ($type !== null) {
            $this->db->where('type', $type);
        }
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 创建分类
     * @param array $data 分类数据
     * @return int|bool 分类ID或false
     */
    public function create_category($data) {
        $this->db->insert('categories', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新分类
     * @param int $id 分类ID
     * @param array $data 分类数据
     * @return bool 是否成功
     */
    public function update_category($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('categories', $data);
    }
    
    /**
     * 删除分类（软删除）
     * @param int $id 分类ID
     * @return bool 是否成功
     */
    public function delete_category($id) {
        $this->db->where('id', $id);
        return $this->db->update('categories', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 检查分类下是否有产品
     * @param int $id 分类ID
     * @return bool 是否有产品
     */
    public function has_products($id) {
        $this->db->from('products');
        $this->db->where('category_id', $id);
        $this->db->where('is_deleted', 0);
        
        return $this->db->count_all_results() > 0;
    }
}
