<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 采购管理控制器
 * 实现采购相关的API接口
 */
class Purchase extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('purchase_model');
        $this->load->model('product_model');
        $this->load->model('supplier_model');
        $this->load->model('inventory_model');
    }

    /**
     * 获取采购订单列表
     * GET /api/purchases
     */
    public function index_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'supplier_id' => $this->get('supplier_id', true),
            'status' => $this->get('status', true),
            'start_date' => $this->get('start_date', true),
            'end_date' => $this->get('end_date', true)
        ];

        // 获取采购订单列表和总数
        $orders = $this->purchase_model->get_orders($params);
        $total = $this->purchase_model->count_orders($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $orders,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取采购订单详情
     * GET /api/purchases/:id
     */
    public function detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '采购订单ID不能为空'
            ], 400);
            return;
        }

        // 获取采购订单详情
        $order = $this->purchase_model->get_order_by_id($id);
        if (!$order) {
            $this->response([
                'status' => false,
                'message' => '采购订单不存在'
            ], 404);
            return;
        }

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $order
        ], 200);
    }

    /**
     * 创建采购订单
     * POST /api/purchases
     */
    public function create_post() {
        // 获取请求数据
        $order_data = [
            'order_no' => $this->post('order_no', true),
            'supplier_id' => $this->post('supplier_id', true),
            'order_date' => $this->post('order_date', true),
            'expected_delivery_date' => $this->post('expected_delivery_date', true),
            'warehouse_id' => $this->post('warehouse_id', true),
            'payment_term' => $this->post('payment_term', true),
            'currency' => $this->post('currency', true),
            'tax_rate' => $this->post('tax_rate', true),
            'discount_amount' => $this->post('discount_amount', true),
            'shipping_fee' => $this->post('shipping_fee', true),
            'remark' => $this->post('remark', true),
            'status' => 'draft',
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 获取订单明细
        $details = json_decode($this->post('details', true), true);

        // 验证必填字段
        if (!$order_data['supplier_id']) {
            $this->response([
                'status' => false,
                'message' => '供应商ID不能为空'
            ], 400);
            return;
        }

        if (!$order_data['order_date']) {
            $this->response([
                'status' => false,
                'message' => '订单日期不能为空'
            ], 400);
            return;
        }

        if (!$order_data['warehouse_id']) {
            $this->response([
                'status' => false,
                'message' => '仓库ID不能为空'
            ], 400);
            return;
        }

        if (empty($details)) {
            $this->response([
                'status' => false,
                'message' => '订单明细不能为空'
            ], 400);
            return;
        }

        // 验证订单明细
        $total_amount = 0;
        $total_tax = 0;
        $processed_details = [];

        foreach ($details as $detail) {
            if (empty($detail['product_id']) || empty($detail['quantity']) || empty($detail['price'])) {
                $this->response([
                    'status' => false,
                    'message' => '订单明细必须包含产品ID、数量和价格'
                ], 400);
                return;
            }

            // 获取产品信息以验证
            $product = $this->product_model->get_product_by_id($detail['product_id']);
            if (!$product) {
                $this->response([
                    'status' => false,
                    'message' => '产品不存在: ' . $detail['product_id']
                ], 400);
                return;
            }

            // 计算明细金额
            $amount = $detail['quantity'] * $detail['price'];
            $tax = 0;
            if (isset($detail['tax_rate']) && $detail['tax_rate'] > 0) {
                $tax = $amount * ($detail['tax_rate'] / 100);
            } elseif ($order_data['tax_rate'] > 0) {
                $tax = $amount * ($order_data['tax_rate'] / 100);
            }

            $total_amount += $amount;
            $total_tax += $tax;

            // 添加到处理后的明细
            $processed_details[] = [
                'product_id' => $detail['product_id'],
                'quantity' => $detail['quantity'],
                'price' => $detail['price'],
                'tax_rate' => isset($detail['tax_rate']) ? $detail['tax_rate'] : $order_data['tax_rate'],
                'amount' => $amount,
                'tax_amount' => $tax,
                'discount_amount' => isset($detail['discount_amount']) ? $detail['discount_amount'] : 0,
                'remark' => isset($detail['remark']) ? $detail['remark'] : null
            ];
        }

        // 计算订单总金额
        $subtotal = $total_amount;
        $discount_amount = isset($order_data['discount_amount']) ? $order_data['discount_amount'] : 0;
        $shipping_fee = isset($order_data['shipping_fee']) ? $order_data['shipping_fee'] : 0;
        $grand_total = $subtotal + $total_tax - $discount_amount + $shipping_fee;

        // 添加计算后的金额到订单数据
        $order_data['subtotal'] = $subtotal;
        $order_data['tax_amount'] = $total_tax;
        $order_data['grand_total'] = $grand_total;

        // 如果没有提供订单编号，自动生成
        if (empty($order_data['order_no'])) {
            $order_data['order_no'] = 'PO' . date('YmdHis');
        }

        // 创建采购订单
        $order_id = $this->purchase_model->create_order($order_data, $processed_details);
        if (!$order_id) {
            $this->response([
                'status' => false,
                'message' => '创建采购订单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建采购订单成功',
            'data' => [
                'id' => $order_id,
                'order_no' => $order_data['order_no']
            ]
        ], 201);
    }

    /**
     * 更新采购订单
     * PUT /api/purchases/:id
     */
    public function update_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '采购订单ID不能为空'
            ], 400);
            return;
        }

        // 检查采购订单是否存在
        $order = $this->purchase_model->get_order_by_id($id);
        if (!$order) {
            $this->response([
                'status' => false,
                'message' => '采购订单不存在'
            ], 404);
            return;
        }

        // 检查是否可以更新
        if ($order['status'] !== 'draft' && $order['status'] !== 'pending') {
            $this->response([
                'status' => false,
                'message' => '只有草稿或待审核状态的采购订单可以更新'
            ], 400);
            return;
        }

        // 获取请求数据
        $order_data = [
            'supplier_id' => $this->put('supplier_id', true),
            'order_date' => $this->put('order_date', true),
            'expected_delivery_date' => $this->put('expected_delivery_date', true),
            'warehouse_id' => $this->put('warehouse_id', true),
            'payment_term' => $this->put('payment_term', true),
            'currency' => $this->put('currency', true),
            'tax_rate' => $this->put('tax_rate', true),
            'discount_amount' => $this->put('discount_amount', true),
            'shipping_fee' => $this->put('shipping_fee', true),
            'remark' => $this->put('remark', true),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 获取订单明细
        $details = json_decode($this->put('details', true), true);

        // 过滤空值
        $order_data = array_filter($order_data, function($value) {
            return $value !== null;
        });

        // 如果有明细数据，需要更新明细
        if (!empty($details)) {
            // 验证订单明细
            $total_amount = 0;
            $total_tax = 0;
            $processed_details = [];

            foreach ($details as $detail) {
                if (empty($detail['product_id']) || empty($detail['quantity']) || empty($detail['price'])) {
                    $this->response([
                        'status' => false,
                        'message' => '订单明细必须包含产品ID、数量和价格'
                    ], 400);
                    return;
                }

                // 获取产品信息以验证
                $product = $this->product_model->get_product_by_id($detail['product_id']);
                if (!$product) {
                    $this->response([
                        'status' => false,
                        'message' => '产品不存在: ' . $detail['product_id']
                    ], 400);
                    return;
                }

                // 计算明细金额
                $amount = $detail['quantity'] * $detail['price'];
                $tax = 0;
                if (isset($detail['tax_rate']) && $detail['tax_rate'] > 0) {
                    $tax = $amount * ($detail['tax_rate'] / 100);
                } elseif (isset($order_data['tax_rate']) && $order_data['tax_rate'] > 0) {
                    $tax = $amount * ($order_data['tax_rate'] / 100);
                } elseif ($order['tax_rate'] > 0) {
                    $tax = $amount * ($order['tax_rate'] / 100);
                }

                $total_amount += $amount;
                $total_tax += $tax;

                // 添加到处理后的明细
                $processed_details[] = [
                    'product_id' => $detail['product_id'],
                    'quantity' => $detail['quantity'],
                    'price' => $detail['price'],
                    'tax_rate' => isset($detail['tax_rate']) ? $detail['tax_rate'] : (isset($order_data['tax_rate']) ? $order_data['tax_rate'] : $order['tax_rate']),
                    'amount' => $amount,
                    'tax_amount' => $tax,
                    'discount_amount' => isset($detail['discount_amount']) ? $detail['discount_amount'] : 0,
                    'remark' => isset($detail['remark']) ? $detail['remark'] : null
                ];
            }

            // 计算订单总金额
            $subtotal = $total_amount;
            $discount_amount = isset($order_data['discount_amount']) ? $order_data['discount_amount'] : $order['discount_amount'];
            $shipping_fee = isset($order_data['shipping_fee']) ? $order_data['shipping_fee'] : $order['shipping_fee'];
            $grand_total = $subtotal + $total_tax - $discount_amount + $shipping_fee;

            // 添加计算后的金额到订单数据
            $order_data['subtotal'] = $subtotal;
            $order_data['tax_amount'] = $total_tax;
            $order_data['grand_total'] = $grand_total;

            // 更新采购订单
            $result = $this->purchase_model->update_order($id, $order_data, $processed_details);
        } else {
            // 只更新订单基本信息
            $result = $this->purchase_model->update_order($id, $order_data);
        }

        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新采购订单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新采购订单成功'
        ], 200);
    }

    /**
     * 更新采购订单状态
     * PUT /api/purchases/:id/status
     */
    public function status_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '采购订单ID不能为空'
            ], 400);
            return;
        }

        // 获取请求数据
        $status = $this->put('status', true);
        if (!$status) {
            $this->response([
                'status' => false,
                'message' => '状态不能为空'
            ], 400);
            return;
        }

        // 检查采购订单是否存在
        $order = $this->purchase_model->get_order_by_id($id);
        if (!$order) {
            $this->response([
                'status' => false,
                'message' => '采购订单不存在'
            ], 404);
            return;
        }

        // 检查状态转换是否有效
        $valid_transitions = [
            'draft' => ['pending', 'cancelled'],
            'pending' => ['confirmed', 'cancelled'],
            'confirmed' => ['completed', 'cancelled'],
            'completed' => [],
            'cancelled' => []
        ];

        if (!in_array($status, $valid_transitions[$order['status']])) {
            $this->response([
                'status' => false,
                'message' => '无效的状态转换：从 ' . $order['status'] . ' 到 ' . $status
            ], 400);
            return;
        }

        // 更新采购订单状态
        $result = $this->purchase_model->update_order_status($id, $status, $this->user_id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新采购订单状态失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新采购订单状态成功'
        ], 200);
    }

    /**
     * 删除采购订单
     * DELETE /api/purchases/:id
     */
    public function delete_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '采购订单ID不能为空'
            ], 400);
            return;
        }

        // 检查采购订单是否存在
        $order = $this->purchase_model->get_order_by_id($id);
        if (!$order) {
            $this->response([
                'status' => false,
                'message' => '采购订单不存在'
            ], 404);
            return;
        }

        // 检查是否可以删除
        if ($order['status'] !== 'draft' && $order['status'] !== 'pending') {
            $this->response([
                'status' => false,
                'message' => '只有草稿或待审核状态的采购订单可以删除'
            ], 400);
            return;
        }

        // 删除采购订单（软删除）
        $result = $this->purchase_model->delete_order($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除采购订单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除采购订单成功'
        ], 200);
    }

    /**
     * 获取采购入库单列表
     * GET /api/purchases/receives
     */
    public function receives_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'supplier_id' => $this->get('supplier_id', true),
            'warehouse_id' => $this->get('warehouse_id', true),
            'status' => $this->get('status', true),
            'start_date' => $this->get('start_date', true),
            'end_date' => $this->get('end_date', true)
        ];

        // 获取采购入库单列表和总数
        $receives = $this->purchase_model->get_receives($params);
        $total = $this->purchase_model->count_receives($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $receives,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取采购入库单详情
     * GET /api/purchases/receives/:id
     */
    public function receive_detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '采购入库单ID不能为空'
            ], 400);
            return;
        }

        // 获取采购入库单详情
        $receive = $this->purchase_model->get_receive_by_id($id);
        if (!$receive) {
            $this->response([
                'status' => false,
                'message' => '采购入库单不存在'
            ], 404);
            return;
        }

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $receive
        ], 200);
    }

    /**
     * 创建采购入库单
     * POST /api/purchases/receives
     */
    public function receive_create_post() {
        // 获取请求数据
        $receive_data = [
            'receive_no' => $this->post('receive_no', true),
            'order_id' => $this->post('order_id', true),
            'supplier_id' => $this->post('supplier_id', true),
            'warehouse_id' => $this->post('warehouse_id', true),
            'receive_date' => $this->post('receive_date', true),
            'remark' => $this->post('remark', true),
            'status' => 'draft',
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 获取入库单明细
        $details = json_decode($this->post('details', true), true);

        // 验证必填字段
        if (!$receive_data['supplier_id']) {
            $this->response([
                'status' => false,
                'message' => '供应商ID不能为空'
            ], 400);
            return;
        }

        if (!$receive_data['warehouse_id']) {
            $this->response([
                'status' => false,
                'message' => '仓库ID不能为空'
            ], 400);
            return;
        }

        if (!$receive_data['receive_date']) {
            $this->response([
                'status' => false,
                'message' => '入库日期不能为空'
            ], 400);
            return;
        }

        if (empty($details)) {
            $this->response([
                'status' => false,
                'message' => '入库单明细不能为空'
            ], 400);
            return;
        }

        // 验证入库单明细
        $processed_details = [];
        foreach ($details as $detail) {
            if (empty($detail['product_id']) || empty($detail['quantity'])) {
                $this->response([
                    'status' => false,
                    'message' => '入库单明细必须包含产品ID和数量'
                ], 400);
                return;
            }

            // 获取产品信息以验证
            $product = $this->product_model->get_product_by_id($detail['product_id']);
            if (!$product) {
                $this->response([
                    'status' => false,
                    'message' => '产品不存在: ' . $detail['product_id']
                ], 400);
                return;
            }

            // 添加到处理后的明细
            $processed_details[] = [
                'product_id' => $detail['product_id'],
                'quantity' => $detail['quantity'],
                'batch_no' => isset($detail['batch_no']) ? $detail['batch_no'] : null,
                'production_date' => isset($detail['production_date']) ? $detail['production_date'] : null,
                'expiry_date' => isset($detail['expiry_date']) ? $detail['expiry_date'] : null,
                'remark' => isset($detail['remark']) ? $detail['remark'] : null
            ];
        }

        // 如果没有提供入库单编号，自动生成
        if (empty($receive_data['receive_no'])) {
            $receive_data['receive_no'] = 'GR' . date('YmdHis');
        }

        // 创建采购入库单
        $receive_id = $this->purchase_model->create_receive($receive_data, $processed_details);
        if (!$receive_id) {
            $this->response([
                'status' => false,
                'message' => '创建采购入库单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建采购入库单成功',
            'data' => [
                'id' => $receive_id,
                'receive_no' => $receive_data['receive_no']
            ]
        ], 201);
    }
}
