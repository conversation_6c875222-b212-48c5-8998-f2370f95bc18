<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 角色模型类
 * 处理角色和权限相关的数据库操作
 */
class Role_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取角色列表
     * @param array $params 查询参数
     * @return array 角色列表
     */
    public function get_roles($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('r.*, u.name as created_by_name');
        $this->db->from('roles r');
        $this->db->join('users u', 'r.created_by = u.id', 'left');
        $this->db->where('r.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('r.name', $keyword);
            $this->db->or_like('r.description', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('r.status', $params['status']);
        }
        
        $this->db->order_by('r.id', 'ASC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取角色总数
     * @param array $params 查询参数
     * @return int 角色总数
     */
    public function count_roles($params = []) {
        $this->db->from('roles r');
        $this->db->where('r.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('r.name', $keyword);
            $this->db->or_like('r.description', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('r.status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 获取所有角色（用于下拉列表）
     * @return array 所有角色
     */
    public function get_all_roles() {
        $this->db->select('id, name');
        $this->db->from('roles');
        $this->db->where('is_deleted', 0);
        $this->db->where('status', 'active');
        $this->db->order_by('id', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 根据ID获取角色信息
     * @param int $id 角色ID
     * @return array|null 角色信息
     */
    public function get_role_by_id($id) {
        $this->db->select('r.*, u.name as created_by_name');
        $this->db->from('roles r');
        $this->db->join('users u', 'r.created_by = u.id', 'left');
        $this->db->where('r.id', $id);
        $this->db->where('r.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 检查角色名称是否存在
     * @param string $name 角色名称
     * @param int $exclude_id 排除的角色ID
     * @return bool 是否存在
     */
    public function check_name_exists($name, $exclude_id = 0) {
        $this->db->from('roles');
        $this->db->where('name', $name);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id > 0) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 创建角色
     * @param array $data 角色数据
     * @return int|bool 角色ID或false
     */
    public function create_role($data) {
        $this->db->insert('roles', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新角色
     * @param int $id 角色ID
     * @param array $data 角色数据
     * @return bool 是否成功
     */
    public function update_role($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('roles', $data);
    }
    
    /**
     * 删除角色（软删除）
     * @param int $id 角色ID
     * @return bool 是否成功
     */
    public function delete_role($id) {
        $this->db->where('id', $id);
        return $this->db->update('roles', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 检查角色是否被引用
     * @param int $id 角色ID
     * @return bool 是否被引用
     */
    public function check_role_in_use($id) {
        $this->db->from('users');
        $this->db->where('role_id', $id);
        $this->db->where('is_deleted', 0);
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 获取所有权限
     * @return array 所有权限
     */
    public function get_all_permissions() {
        $this->db->select('*');
        $this->db->from('permissions');
        $this->db->order_by('module', 'ASC');
        $this->db->order_by('order', 'ASC');
        
        $query = $this->db->get();
        $permissions = $query->result_array();
        
        // 按模块分组
        $result = [];
        foreach ($permissions as $permission) {
            $module = $permission['module'];
            if (!isset($result[$module])) {
                $result[$module] = [
                    'module' => $module,
                    'name' => $this->get_module_name($module),
                    'permissions' => []
                ];
            }
            
            $result[$module]['permissions'][] = [
                'id' => $permission['id'],
                'code' => $permission['code'],
                'name' => $permission['name'],
                'description' => $permission['description']
            ];
        }
        
        return array_values($result);
    }
    
    /**
     * 获取角色权限
     * @param int $role_id 角色ID
     * @return array 角色权限
     */
    public function get_role_permissions($role_id) {
        $this->db->select('p.code');
        $this->db->from('role_permissions rp');
        $this->db->join('permissions p', 'rp.permission_id = p.id', 'left');
        $this->db->where('rp.role_id', $role_id);
        
        $query = $this->db->get();
        $permissions = $query->result_array();
        
        $result = [];
        foreach ($permissions as $permission) {
            $result[] = $permission['code'];
        }
        
        return $result;
    }
    
    /**
     * 更新角色权限
     * @param int $role_id 角色ID
     * @param array $permissions 权限ID数组
     * @return bool 是否成功
     */
    public function update_role_permissions($role_id, $permissions) {
        // 删除原有权限
        $this->db->where('role_id', $role_id);
        $this->db->delete('role_permissions');
        
        // 添加新权限
        if (is_array($permissions) && count($permissions) > 0) {
            $data = [];
            foreach ($permissions as $permission_id) {
                $data[] = [
                    'role_id' => $role_id,
                    'permission_id' => $permission_id
                ];
            }
            
            return $this->db->insert_batch('role_permissions', $data);
        }
        
        return true;
    }
    
    /**
     * 获取模块名称
     * @param string $module 模块代码
     * @return string 模块名称
     */
    private function get_module_name($module) {
        $module_names = [
            'dashboard' => '仪表盘',
            'user' => '用户管理',
            'role' => '角色管理',
            'customer' => '客户管理',
            'supplier' => '供应商管理',
            'product' => '产品管理',
            'category' => '分类管理',
            'inventory' => '库存管理',
            'purchase' => '采购管理',
            'sales' => '销售管理',
            'finance' => '财务管理',
            'report' => '报表管理',
            'setting' => '系统设置'
        ];
        
        return isset($module_names[$module]) ? $module_names[$module] : $module;
    }
}
