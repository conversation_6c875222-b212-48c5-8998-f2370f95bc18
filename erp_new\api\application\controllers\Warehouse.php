<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 仓库管理控制器
 * 实现仓库相关的API接口
 */
class Warehouse extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('warehouse_model');
    }

    /**
     * 获取仓库列表
     * GET /api/warehouses
     */
    public function index_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? $this->get('page', true) : 1,
            'limit' => $this->get('limit', true) ? $this->get('limit', true) : 10,
            'keyword' => $this->get('keyword', true),
            'status' => $this->get('status', true),
            'no_paging' => $this->get('no_paging', true)
        ];

        // 获取仓库列表和总数
        $warehouses = $this->warehouse_model->get_warehouses($params);
        $total = $this->warehouse_model->count_warehouses($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $warehouses,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取单个仓库详情
     * GET /api/warehouses/:id
     */
    public function detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '仓库ID不能为空'
            ], 400);
            return;
        }

        $warehouse = $this->warehouse_model->get_warehouse_by_id($id);
        if (!$warehouse) {
            $this->response([
                'status' => false,
                'message' => '仓库不存在'
            ], 404);
            return;
        }

        $this->response([
            'status' => true,
            'data' => $warehouse
        ], 200);
    }

    /**
     * 创建新仓库
     * POST /api/warehouses
     */
    public function create_post() {
        // 验证输入数据
        $this->validate_warehouse_data();

        // 获取请求数据
        $data = [
            'name' => $this->post('name', true),
            'code' => $this->post('code', true),
            'address' => $this->post('address', true),
            'contact' => $this->post('contact', true),
            'phone' => $this->post('phone', true),
            'sort_order' => $this->post('sort_order', true) ? $this->post('sort_order', true) : 0,
            'status' => $this->post('status', true) ? $this->post('status', true) : 1,
            'description' => $this->post('description', true),
            'created_by' => $this->get_user_id(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 检查仓库编码是否已存在
        $existing = $this->warehouse_model->get_warehouse_by_code($data['code']);
        if ($existing) {
            $this->response([
                'status' => false,
                'message' => '仓库编码已存在'
            ], 400);
            return;
        }

        // 检查仓库名称是否已存在
        $existing = $this->warehouse_model->get_warehouse_by_name($data['name']);
        if ($existing) {
            $this->response([
                'status' => false,
                'message' => '仓库名称已存在'
            ], 400);
            return;
        }

        // 创建仓库
        $warehouse_id = $this->warehouse_model->create_warehouse($data);
        if (!$warehouse_id) {
            $this->response([
                'status' => false,
                'message' => '创建仓库失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '创建仓库成功',
            'data' => ['id' => $warehouse_id]
        ], 201);
    }

    /**
     * 更新仓库
     * PUT /api/warehouses/:id
     */
    public function update_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '仓库ID不能为空'
            ], 400);
            return;
        }

        // 检查仓库是否存在
        $warehouse = $this->warehouse_model->get_warehouse_by_id($id);
        if (!$warehouse) {
            $this->response([
                'status' => false,
                'message' => '仓库不存在'
            ], 404);
            return;
        }

        // 验证输入数据
        $this->validate_warehouse_data(true);

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'code' => $this->put('code', true),
            'address' => $this->put('address', true),
            'contact' => $this->put('contact', true),
            'phone' => $this->put('phone', true),
            'sort_order' => $this->put('sort_order', true) ? $this->put('sort_order', true) : 0,
            'status' => $this->put('status', true) ? $this->put('status', true) : 1,
            'description' => $this->put('description', true),
            'updated_by' => $this->get_user_id(),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 检查仓库编码是否已存在（排除当前仓库）
        $existing = $this->warehouse_model->get_warehouse_by_code($data['code']);
        if ($existing && $existing['id'] != $id) {
            $this->response([
                'status' => false,
                'message' => '仓库编码已存在'
            ], 400);
            return;
        }

        // 检查仓库名称是否已存在（排除当前仓库）
        $existing = $this->warehouse_model->get_warehouse_by_name($data['name']);
        if ($existing && $existing['id'] != $id) {
            $this->response([
                'status' => false,
                'message' => '仓库名称已存在'
            ], 400);
            return;
        }

        // 更新仓库
        $result = $this->warehouse_model->update_warehouse($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新仓库失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '更新仓库成功'
        ], 200);
    }

    /**
     * 删除仓库
     * DELETE /api/warehouses/:id
     */
    public function delete_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '仓库ID不能为空'
            ], 400);
            return;
        }

        // 检查仓库是否存在
        $warehouse = $this->warehouse_model->get_warehouse_by_id($id);
        if (!$warehouse) {
            $this->response([
                'status' => false,
                'message' => '仓库不存在'
            ], 404);
            return;
        }

        // 检查仓库是否在使用中
        if ($this->warehouse_model->is_warehouse_in_use($id)) {
            $this->response([
                'status' => false,
                'message' => '该仓库正在使用中，无法删除'
            ], 400);
            return;
        }

        // 删除仓库
        $result = $this->warehouse_model->delete_warehouse($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除仓库失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '删除仓库成功'
        ], 200);
    }

    /**
     * 批量删除仓库
     * POST /api/warehouses/batch-delete
     */
    public function batch_delete_post() {
        $ids = $this->post('ids');
        if (!$ids || !is_array($ids) || empty($ids)) {
            $this->response([
                'status' => false,
                'message' => '请选择要删除的仓库'
            ], 400);
            return;
        }

        // 检查是否有仓库在使用中
        foreach ($ids as $id) {
            if ($this->warehouse_model->is_warehouse_in_use($id)) {
                $warehouse = $this->warehouse_model->get_warehouse_by_id($id);
                $this->response([
                    'status' => false,
                    'message' => "仓库 {$warehouse['name']} 正在使用中，无法删除"
                ], 400);
                return;
            }
        }

        // 批量删除仓库
        $result = $this->warehouse_model->batch_delete_warehouses($ids);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '批量删除仓库失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '批量删除仓库成功'
        ], 200);
    }

    /**
     * 批量更新仓库
     * POST /api/warehouses/batch-update
     */
    public function batch_update_post() {
        $warehouses = $this->post('warehouses');
        if (!$warehouses || !is_array($warehouses) || empty($warehouses)) {
            $this->response([
                'status' => false,
                'message' => '请提供要更新的仓库数据'
            ], 400);
            return;
        }

        // 批量更新仓库
        $result = $this->warehouse_model->batch_update_warehouses($warehouses);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '批量更新仓库失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '批量更新仓库成功'
        ], 200);
    }

    /**
     * 验证仓库数据
     * @param bool $is_update 是否为更新操作
     */
    private function validate_warehouse_data($is_update = false) {
        $this->form_validation->set_data($is_update ? $this->put() : $this->post());
        $this->form_validation->set_rules('name', '仓库名称', 'required|trim');
        $this->form_validation->set_rules('code', '仓库编码', 'required|trim');

        if (!$this->form_validation->run()) {
            $this->response([
                'status' => false,
                'message' => validation_errors()
            ], 400);
            exit;
        }
    }
}
