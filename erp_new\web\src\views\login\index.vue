<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="@/assets/logo.png" alt="Logo" class="logo" />
        <h2 class="title">u4e91u8fdbu9500u5b58u7cfbu7edf</h2>
      </div>

      <el-form ref="loginFormRef" :model="loginForm" :rules="loginRules" class="login-form">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="u7528u6237u540d"
            prefix-icon="User"
            size="large"
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            placeholder="u5bc6u7801"
            prefix-icon="Lock"
            size="large"
            show-password
            clearable
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="loginForm.rememberMe">u8bb0u4f4fu5bc6u7801</el-checkbox>
        </el-form-item>

        <el-button :loading="loading" type="primary" size="large" class="login-button" @click="handleLogin">
          {{ loading ? 'u767bu5f55u4e2d...' : 'u767bu5f55' }}
        </el-button>
      </el-form>

      <div class="login-footer">
        <p>u00a9 {{ new Date().getFullYear() }} u4e91u8fdbu9500u5b58u7cfbu7edf - u5168u65b0u754cu9762u5347u7ea7u7248</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { validUsername } from '@/utils/validate'

const router = useRouter()
const userStore = useUserStore()
const loginFormRef = ref(null)

// u767bu5f55u8868u5355u6570u636e
const loginForm = reactive({
  username: '',
  password: '',
  rememberMe: false
})

// u8868u5355u9a8cu8bc1u89c4u5219
const loginRules = {
  username: [
    { required: true, trigger: 'blur', message: 'u8bf7u8f93u5165u7528u6237u540d' },
    { validator: (rule, value, callback) => {
      if (!validUsername(value) && value !== 'admin') {
        callback(new Error('u7528u6237u540du683cu5f0fu4e0du6b63u786e'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ],
  password: [
    { required: true, trigger: 'blur', message: 'u8bf7u8f93u5165u5bc6u7801' },
    { min: 6, message: 'u5bc6u7801u957fu5ea6u4e0du80fdu5c11u4e8e6u4f4d', trigger: 'blur' }
  ]
}

// u52a0u8f7du72b6u6001
const loading = ref(false)

// u521du59cbu5316
onMounted(() => {
  // u5c1du8bd5u4eceu672cu5730u5b58u50a8u6062u590du767bu5f55u4fe1u606f
  const username = localStorage.getItem('username')
  const password = localStorage.getItem('password')
  if (username && password) {
    loginForm.username = username
    loginForm.password = password
    loginForm.rememberMe = true
  }
})

// 处理登录
const handleLogin = () => {
  loginFormRef.value.validate(async valid => {
    if (!valid) return

    try {
      loading.value = true

      // 调用登录操作
      const res = await userStore.loginAction(
        loginForm.username,
        loginForm.password,
        loginForm.rememberMe
      )

      // 如果选择了记住密码
      if (loginForm.rememberMe) {
        localStorage.setItem('remembered_username', loginForm.username)
        localStorage.setItem('remembered_password', loginForm.password)
        localStorage.setItem('remember_me', 'true')
      } else {
        localStorage.removeItem('remembered_username')
        localStorage.removeItem('remembered_password')
        localStorage.removeItem('remember_me')
      }

      ElMessage.success('登录成功')

      // 跳转到首页或重定向页面
      const redirect = route.query.redirect || '/'
      router.push({ path: redirect })
    } catch (error) {
      console.error('登录错误:', error)
      ElMessage.error(error || '登录失败')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .login-box {
    width: 400px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    padding: 30px;
    overflow: hidden;

    .login-header {
      text-align: center;
      margin-bottom: 30px;

      .logo {
        height: 60px;
        margin-bottom: 15px;
      }

      .title {
        font-size: 24px;
        color: #333;
        margin: 0;
      }
    }

    .login-form {
      margin-bottom: 20px;

      .login-button {
        width: 100%;
        font-size: 16px;
        letter-spacing: 1px;
        border-radius: 6px;
      }
    }

    .login-footer {
      text-align: center;
      color: #888;
      font-size: 12px;
    }
  }
}

@media (max-width: 768px) {
  .login-container .login-box {
    width: 80%;
    padding: 20px;
  }
}
</style>
