<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 认证控制器
 *
 * 处理用户登录、注册和令牌管理
 */
class Auth extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('auth_model');
        $this->load->helper('security');
    }

    /**
     * 用户登录
     */
    public function login() {
        // 验证请求数据
        if (!isset($this->request_data['username']) || !isset($this->request_data['password'])) {
            $this->error('用户名和密码不能为空');
            return;
        }

        $username = trim($this->request_data['username']);
        $password = trim($this->request_data['password']);
        $remember_me = isset($this->request_data['rememberMe']) ? $this->request_data['rememberMe'] : false;

        // 基本验证
        if (empty($username)) {
            $this->error('用户名不能为空');
            return;
        }

        if (empty($password)) {
            $this->error('密码不能为空');
            return;
        }

        try {
            // 调用模型验证用户
            $result = $this->auth_model->login($username, $password);

            if (!$result['status']) {
                $this->error($result['message']);
                return;
            }

            $user = $result['data'];

            // 生成访问令牌
            $token_data = $this->auth_model->create_token($user['uid']);

            // 记录登录日志
            $this->_log_user_action('用户登录', '用户名：' . $username, $user['uid']);

            // 返回用户信息和令牌
            $user_data = [
                'uid' => $user['uid'],
                'username' => $user['username'],
                'name' => $user['name'],
                'role_id' => $user['roleid'],
                'access_token' => $token_data['token'],
                'token_type' => 'Bearer',
                'expires_in' => $token_data['expires_in'],
                'permissions' => $result['permissions'] ?? []
            ];

            $this->success($user_data, '登录成功');

        } catch (Exception $e) {
            log_message('error', '登录错误: ' . $e->getMessage());
            $this->error('服务器内部错误，请稍后重试');
        }
    }

    /**
     * 用户退出
     */
    public function logout() {
        try {
            // 已在基类中验证了访问令牌
            $this->auth_model->revoke_token($this->token);

            // 记录登出日志
            $this->_log_user_action('用户登出', '令牌：' . substr($this->token, 0, 10) . '...', $this->user_id);

            $this->success([], '退出成功');

        } catch (Exception $e) {
            log_message('error', '登出错误: ' . $e->getMessage());
            $this->error('登出失败');
        }
    }

    /**
     * 刷新访问令牌
     */
    public function refresh_token() {
        try {
            // 已在基类中验证了访问令牌
            $token_data = $this->auth_model->refresh_token($this->token, $this->user_id);

            if (!$token_data) {
                $this->error('无法刷新令牌');
                return;
            }

            $response = [
                'access_token' => $token_data['token'],
                'token_type' => 'Bearer',
                'expires_in' => $token_data['expires_in']
            ];

            $this->success($response, '令牌刷新成功');

        } catch (Exception $e) {
            log_message('error', '刷新令牌错误: ' . $e->getMessage());
            $this->error('刷新令牌失败');
        }
    }

    /**
     * 获取当前用户信息
     */
    public function user_info() {
        try {
            $user_info = $this->auth_model->get_user_info($this->user_id);

            if (!$user_info) {
                $this->error('用户信息不存在');
                return;
            }

            $this->success($user_info, '获取用户信息成功');

        } catch (Exception $e) {
            log_message('error', '获取用户信息错误: ' . $e->getMessage());
            $this->error('获取用户信息失败');
        }
    }

    /**
     * 修改密码
     */
    public function change_password() {
        // 验证请求数据
        if (!isset($this->request_data['old_password']) ||
            !isset($this->request_data['new_password']) ||
            !isset($this->request_data['confirm_password'])) {
            $this->error('所有字段都不能为空');
            return;
        }

        $old_password = trim($this->request_data['old_password']);
        $new_password = trim($this->request_data['new_password']);
        $confirm_password = trim($this->request_data['confirm_password']);

        // 验证输入
        if (empty($old_password) || empty($new_password) || empty($confirm_password)) {
            $this->error('所有字段都不能为空');
            return;
        }

        if ($new_password !== $confirm_password) {
            $this->error('新密码和确认密码不一致');
            return;
        }

        if (strlen($new_password) < 6) {
            $this->error('新密码长度不能少于6位');
            return;
        }

        try {
            $result = $this->auth_model->change_password($this->user_id, $old_password, $new_password);

            if (!$result['status']) {
                $this->error($result['message']);
                return;
            }

            // 记录修改密码日志
            $this->_log_user_action('修改密码', '用户ID：' . $this->user_id, $this->user_id);

            $this->success([], '密码修改成功');

        } catch (Exception $e) {
            log_message('error', '修改密码错误: ' . $e->getMessage());
            $this->error('修改密码失败');
        }
    }

    /**
     * 验证令牌有效性
     */
    public function verify_token() {
        // 如果能到达这里，说明令牌已经通过基类验证
        $this->success([
            'user_id' => $this->user_id,
            'token' => $this->token
        ], '令牌有效');
    }

    /**
     * 记录用户操作日志
     */
    private function _log_user_action($action, $description = '', $user_id = 0) {
        try {
            $this->load->model('user_model');
            $this->user_model->add_log([
                'user_id' => $user_id ?: $this->user_id,
                'action' => $action,
                'description' => $description,
                'ip_address' => $this->input->ip_address(),
                'user_agent' => $this->input->user_agent(),
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            log_message('error', '记录日志错误: ' . $e->getMessage());
        }
    }
}
