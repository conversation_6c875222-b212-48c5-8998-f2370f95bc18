<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 用户管理控制器
 * 实现用户相关的API接口
 */
class User extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('user_model');
        $this->load->model('role_model');
    }

    /**
     * 用户登录
     * POST /api/users/login
     */
    public function login_post() {
        // 获取请求数据
        $username = $this->post('username', true);
        $password = $this->post('password', true);

        // 验证请求数据
        if (!$username || !$password) {
            $this->response([
                'status' => false,
                'message' => '用户名和密码不能为空'
            ], 400);
            return;
        }

        // 验证用户名和密码
        $user = $this->user_model->check_login($username, $password);
        if (!$user) {
            $this->response([
                'status' => false,
                'message' => '用户名或密码错误'
            ], 401);
            return;
        }

        // 检查用户状态
        if ($user['status'] != 'active') {
            $this->response([
                'status' => false,
                'message' => '用户已被禁用，请联系管理员'
            ], 403);
            return;
        }

        // 生成访问令牌
        $token_data = [
            'id' => $user['id'],
            'username' => $user['username'],
            'name' => $user['name'],
            'role_id' => $user['role_id'],
            'time' => time()
        ];

        $token = $this->generate_token($token_data);

        // 更新用户最后登录时间
        $this->user_model->update_last_login($user['id']);

        // 获取用户权限
        $permissions = $this->role_model->get_role_permissions($user['role_id']);

        // 返回登录成功响应
        $this->response([
            'status' => true,
            'message' => '登录成功',
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'role_id' => $user['role_id'],
                    'role_name' => $user['role_name']
                ],
                'permissions' => $permissions
            ]
        ], 200);
    }

    /**
     * 获取当前用户信息
     * GET /api/users/profile
     */
    public function profile_get() {
        // 获取用户信息
        $user = $this->user_model->get_user_by_id($this->user_id);
        if (!$user) {
            $this->response([
                'status' => false,
                'message' => '用户不存在'
            ], 404);
            return;
        }

        // 获取用户权限
        $permissions = $this->role_model->get_role_permissions($user['role_id']);

        // 返回用户信息
        $this->response([
            'status' => true,
            'data' => [
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'name' => $user['name'],
                    'email' => $user['email'],
                    'phone' => $user['phone'],
                    'role_id' => $user['role_id'],
                    'role_name' => $user['role_name'],
                    'last_login' => $user['last_login']
                ],
                'permissions' => $permissions
            ]
        ], 200);
    }

    /**
     * 更新当前用户密码
     * PUT /api/users/password
     */
    public function password_put() {
        // 获取请求数据
        $old_password = $this->put('old_password', true);
        $new_password = $this->put('new_password', true);
        $confirm_password = $this->put('confirm_password', true);

        // 验证请求数据
        if (!$old_password || !$new_password || !$confirm_password) {
            $this->response([
                'status' => false,
                'message' => '原密码、新密码和确认密码不能为空'
            ], 400);
            return;
        }

        if ($new_password != $confirm_password) {
            $this->response([
                'status' => false,
                'message' => '新密码和确认密码不一致'
            ], 400);
            return;
        }

        if (strlen($new_password) < 6) {
            $this->response([
                'status' => false,
                'message' => '密码长度不能少于6位'
            ], 400);
            return;
        }

        // 验证原密码
        $user = $this->user_model->get_user_by_id($this->user_id);
        if (!$user) {
            $this->response([
                'status' => false,
                'message' => '用户不存在'
            ], 404);
            return;
        }

        if (!password_verify($old_password, $user['password'])) {
            $this->response([
                'status' => false,
                'message' => '原密码错误'
            ], 400);
            return;
        }

        // 更新密码
        $result = $this->user_model->update_password($this->user_id, $new_password);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '密码更新失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '密码更新成功'
        ], 200);
    }

    /**
     * 获取用户列表
     * GET /api/users
     */
    public function index_get() {
        // 检查权限
        if (!$this->has_permission('user_view')) {
            $this->response([
                'status' => false,
                'message' => '无权限查看用户列表'
            ], 403);
            return;
        }

        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'role_id' => $this->get('role_id', true),
            'status' => $this->get('status', true)
        ];

        // 获取用户列表和总数
        $users = $this->user_model->get_users($params);
        $total = $this->user_model->count_users($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $users,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取单个用户详情
     * GET /api/users/:id
     */
    public function detail_get($id) {
        // 检查权限
        if (!$this->has_permission('user_view')) {
            $this->response([
                'status' => false,
                'message' => '无权限查看用户详情'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '用户ID不能为空'
            ], 400);
            return;
        }

        // 获取用户详情
        $user = $this->user_model->get_user_by_id($id);
        if (!$user) {
            $this->response([
                'status' => false,
                'message' => '用户不存在'
            ], 404);
            return;
        }

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'name' => $user['name'],
                'email' => $user['email'],
                'phone' => $user['phone'],
                'role_id' => $user['role_id'],
                'role_name' => $user['role_name'],
                'status' => $user['status'],
                'last_login' => $user['last_login'],
                'created_at' => $user['created_at']
            ]
        ], 200);
    }

    /**
     * 创建用户
     * POST /api/users
     */
    public function create_post() {
        // 检查权限
        if (!$this->has_permission('user_add')) {
            $this->response([
                'status' => false,
                'message' => '无权限创建用户'
            ], 403);
            return;
        }

        // 获取请求数据
        $data = [
            'username' => $this->post('username', true),
            'password' => $this->post('password', true),
            'name' => $this->post('name', true),
            'email' => $this->post('email', true),
            'phone' => $this->post('phone', true),
            'role_id' => $this->post('role_id', true),
            'status' => $this->post('status', true) ? $this->post('status', true) : 'active',
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$data['username'] || !$data['password'] || !$data['name'] || !$data['role_id']) {
            $this->response([
                'status' => false,
                'message' => '用户名、密码、姓名和角色不能为空'
            ], 400);
            return;
        }

        // 验证用户名唯一性
        if ($this->user_model->check_username_exists($data['username'])) {
            $this->response([
                'status' => false,
                'message' => '用户名已存在'
            ], 400);
            return;
        }

        // 验证角色是否存在
        $role = $this->role_model->get_role_by_id($data['role_id']);
        if (!$role) {
            $this->response([
                'status' => false,
                'message' => '角色不存在'
            ], 400);
            return;
        }

        // 创建用户
        $user_id = $this->user_model->create_user($data);
        if (!$user_id) {
            $this->response([
                'status' => false,
                'message' => '创建用户失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建用户成功',
            'data' => [
                'id' => $user_id
            ]
        ], 201);
    }

    /**
     * 更新用户
     * PUT /api/users/:id
     */
    public function update_put($id) {
        // 检查权限
        if (!$this->has_permission('user_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新用户'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '用户ID不能为空'
            ], 400);
            return;
        }

        // 检查用户是否存在
        $user = $this->user_model->get_user_by_id($id);
        if (!$user) {
            $this->response([
                'status' => false,
                'message' => '用户不存在'
            ], 404);
            return;
        }

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'email' => $this->put('email', true),
            'phone' => $this->put('phone', true),
            'role_id' => $this->put('role_id', true),
            'status' => $this->put('status', true),
            'updated_by' => $this->user_id,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 如果提供了新密码，更新密码
        $password = $this->put('password', true);
        if ($password) {
            if (strlen($password) < 6) {
                $this->response([
                    'status' => false,
                    'message' => '密码长度不能少于6位'
                ], 400);
                return;
            }
            $data['password'] = password_hash($password, PASSWORD_DEFAULT);
        }

        // 验证必填字段
        if (isset($data['name']) && !$data['name']) {
            $this->response([
                'status' => false,
                'message' => '姓名不能为空'
            ], 400);
            return;
        }

        if (isset($data['role_id']) && !$data['role_id']) {
            $this->response([
                'status' => false,
                'message' => '角色不能为空'
            ], 400);
            return;
        }

        // 验证角色是否存在
        if (isset($data['role_id'])) {
            $role = $this->role_model->get_role_by_id($data['role_id']);
            if (!$role) {
                $this->response([
                    'status' => false,
                    'message' => '角色不存在'
                ], 400);
                return;
            }
        }

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 更新用户
        $result = $this->user_model->update_user($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新用户失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新用户成功'
        ], 200);
    }

    /**
     * 删除用户
     * DELETE /api/users/:id
     */
    public function delete_delete($id) {
        // 检查权限
        if (!$this->has_permission('user_delete')) {
            $this->response([
                'status' => false,
                'message' => '无权限删除用户'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '用户ID不能为空'
            ], 400);
            return;
        }

        // 检查用户是否存在
        $user = $this->user_model->get_user_by_id($id);
        if (!$user) {
            $this->response([
                'status' => false,
                'message' => '用户不存在'
            ], 404);
            return;
        }

        // 不能删除自己
        if ($id == $this->user_id) {
            $this->response([
                'status' => false,
                'message' => '不能删除自己'
            ], 400);
            return;
        }

        // 不能删除超级管理员
        if ($user['role_id'] == 1) {
            $this->response([
                'status' => false,
                'message' => '不能删除超级管理员'
            ], 400);
            return;
        }

        // 删除用户
        $result = $this->user_model->delete_user($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除用户失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除用户成功'
        ], 200);
    }
}
