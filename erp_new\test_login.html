<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ERP系统登录测试</h1>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="123456" placeholder="请输入密码">
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="rememberMe"> 记住密码
            </label>
        </div>
        
        <button onclick="testLogin()">登录测试</button>
        <button onclick="testUserInfo()">获取用户信息</button>
        <button onclick="testLogout()">登出测试</button>
        <button onclick="clearResult()">清除结果</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        let accessToken = '';
        
        // API基础URL
        const API_BASE = 'http://localhost/jinxiaocun/erp_new/api/index.php';
        
        function showResult(data, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result ' + (isError ? 'error' : 'success');
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        function clearResult() {
            document.getElementById('result').style.display = 'none';
        }
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            if (!username || !password) {
                showResult({error: '请输入用户名和密码'}, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        rememberMe: rememberMe
                    })
                });
                
                const data = await response.json();
                
                if (data.status) {
                    accessToken = data.data.access_token;
                    showResult({
                        message: '登录成功',
                        data: data
                    });
                } else {
                    showResult({
                        message: '登录失败',
                        error: data.message
                    }, true);
                }
            } catch (error) {
                showResult({
                    message: '请求失败',
                    error: error.message
                }, true);
            }
        }
        
        async function testUserInfo() {
            if (!accessToken) {
                showResult({error: '请先登录获取访问令牌'}, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/user_info`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                showResult({
                    message: '获取用户信息',
                    data: data
                });
            } catch (error) {
                showResult({
                    message: '请求失败',
                    error: error.message
                }, true);
            }
        }
        
        async function testLogout() {
            if (!accessToken) {
                showResult({error: '请先登录获取访问令牌'}, true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/auth/logout`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                
                if (data.status) {
                    accessToken = '';
                    showResult({
                        message: '登出成功',
                        data: data
                    });
                } else {
                    showResult({
                        message: '登出失败',
                        error: data.message
                    }, true);
                }
            } catch (error) {
                showResult({
                    message: '请求失败',
                    error: error.message
                }, true);
            }
        }
        
        // 页面加载时检查是否有保存的登录信息
        window.onload = function() {
            const savedUsername = localStorage.getItem('remembered_username');
            const savedPassword = localStorage.getItem('remembered_password');
            const rememberMe = localStorage.getItem('remember_me') === 'true';
            
            if (savedUsername) {
                document.getElementById('username').value = savedUsername;
            }
            if (savedPassword) {
                document.getElementById('password').value = savedPassword;
            }
            document.getElementById('rememberMe').checked = rememberMe;
        };
    </script>
</body>
</html>
