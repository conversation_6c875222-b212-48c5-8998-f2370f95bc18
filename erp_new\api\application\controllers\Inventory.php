<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Inventory extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->helper('url');
        $this->load->model('inventory_model');
        $this->load->model('product_model');
    }

    /**
     * 获取库存调拨记录列表
     */
    public function transfers() {
        // 判断请求方法类型
        if ($this->input->method() === 'post') {
            // 创建新的库存调拨记录
            return $this->create_transfer();
        }
        
        // 获取查询参数
        $params = [
            'page' => $this->input->get('page') ? intval($this->input->get('page')) : 1,
            'limit' => $this->input->get('limit') ? intval($this->input->get('limit')) : 10,
            'keyword' => $this->input->get('keyword'),
            'status' => $this->input->get('status'),
            'start_date' => $this->input->get('start_date'),
            'end_date' => $this->input->get('end_date'),
            'source_warehouse_id' => $this->input->get('source_warehouse_id') ? intval($this->input->get('source_warehouse_id')) : null,
            'target_warehouse_id' => $this->input->get('target_warehouse_id') ? intval($this->input->get('target_warehouse_id')) : null
        ];
        
        // 获取库存调拨记录列表和总数
        $transfers = $this->inventory_model->get_transfers($params);
        $total = $this->inventory_model->count_transfers($params);
        
        // 处理返回数据
        $list = [];
        foreach ($transfers as $transfer) {
            $list[] = [
                'id' => $transfer['id'],
                'transfer_no' => $transfer['transfer_no'],
                'transfer_date' => $transfer['transfer_date'],
                'source_warehouse' => $transfer['source_warehouse_name'],
                'source_warehouse_id' => $transfer['source_warehouse_id'],
                'target_warehouse' => $transfer['target_warehouse_name'],
                'target_warehouse_id' => $transfer['target_warehouse_id'],
                'product_count' => $transfer['product_count'],
                'total_quantity' => $transfer['total_quantity'],
                'status' => $transfer['status'],
                'creator' => $transfer['creator_name'],
                'create_time' => $transfer['created_at'],
                'complete_time' => $transfer['completed_at']
            ];
        }

        $data = [
            'status' => true,
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 获取库存调拨记录详情
     */
    public function transfer_detail($id) {
        if (!$id) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => false, 'message' => '调拨单ID不能为空'], JSON_UNESCAPED_UNICODE))
                ->set_status_header(400);
            return;
        }
        
        // 获取调拨单详情
        $transfer_data = $this->inventory_model->get_transfer_by_id($id);
        
        if (!$transfer_data) {
            $this->output
                ->set_content_type('application/json')
                ->set_output(json_encode(['status' => false, 'message' => '调拨单不存在'], JSON_UNESCAPED_UNICODE))
                ->set_status_header(404);
            return;
        }
        
        // 整理数据格式
        $transfer = [
            'id' => $transfer_data['id'],
            'transfer_no' => $transfer_data['transfer_no'],
            'transfer_date' => $transfer_data['transfer_date'],
            'source_warehouse' => $transfer_data['source_warehouse_name'],
            'source_warehouse_id' => $transfer_data['source_warehouse_id'],
            'target_warehouse' => $transfer_data['target_warehouse_name'],
            'target_warehouse_id' => $transfer_data['target_warehouse_id'],
            'product_count' => $transfer_data['product_count'],
            'total_quantity' => $transfer_data['total_quantity'],
            'status' => $transfer_data['status'],
            'creator' => $transfer_data['creator_name'],
            'create_time' => $transfer_data['created_at'],
            'complete_time' => $transfer_data['completed_at'],
            'items' => []
        ];
        
        // 处理调拨单详情项
        foreach ($transfer_data['details'] as $detail) {
            $transfer['items'][] = [
                'product_id' => $detail['product_id'],
                'product_code' => $detail['product_code'],
                'product_name' => $detail['product_name'],
                'quantity' => $detail['quantity'],
                'unit' => $detail['unit'] ?: '',
                'remarks' => $detail['remarks'] ?: ''
            ];
        }

        $data = [
            'status' => 'success',
            'data' => $transfer
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 获取仓库列表
     */
    public function warehouses() {
        // 模拟数据
        $warehouses = [
            ['id' => 1, 'name' => '主仓库', 'code' => 'WH001', 'status' => 'active'],
            ['id' => 2, 'name' => '备用仓库', 'code' => 'WH002', 'status' => 'active'],
            ['id' => 3, 'name' => '原材料仓库', 'code' => 'WH003', 'status' => 'active'],
            ['id' => 4, 'name' => '成品仓库', 'code' => 'WH004', 'status' => 'active'],
            ['id' => 5, 'name' => '退货仓库', 'code' => 'WH005', 'status' => 'active']
        ];

        $data = [
            'status' => 'success',
            'data' => $warehouses
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 创建新的库存调拨记录
     */
    private function create_transfer() {
        // 获取请求数据
        $input_data = json_decode($this->input->raw_input_stream, true);
        
        if (empty($input_data)) {
            $response = [
                'status' => 'error',
                'message' => '无效的请求数据'
            ];
            
            $this->output
                ->set_status_header(400)
                ->set_content_type('application/json')
                ->set_output(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            return;
        }
        
        // 生成调拨单编号
        $transfer_no = 'TR' . date('Ymd') . sprintf('%03d', rand(1, 999));
        
        // 模拟创建成功
        $new_transfer = [
            'id' => rand(10, 100),
            'transfer_no' => $transfer_no,
            'transfer_date' => $input_data['transfer_date'] ?? date('Y-m-d'),
            'source_warehouse' => $input_data['source_warehouse'] ?? '',
            'source_warehouse_id' => $input_data['source_warehouse_id'] ?? 0,
            'target_warehouse' => $input_data['target_warehouse'] ?? '',
            'target_warehouse_id' => $input_data['target_warehouse_id'] ?? 0,
            'status' => 'draft',
            'creator' => '系统管理员',
            'create_time' => date('Y-m-d H:i:s'),
            'items' => $input_data['items'] ?? []
        ];
        
        $response = [
            'status' => 'success',
            'message' => '库存调拨单创建成功',
            'data' => $new_transfer
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 提交库存调拨单审核
     */
    public function transfer_submit($id) {
        $response = [
            'status' => 'success',
            'message' => '调拨单已提交审核',
            'data' => [
                'id' => $id,
                'status' => 'pending',
                'submit_time' => date('Y-m-d H:i:s')
            ]
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 审核库存调拨单
     */
    public function transfer_approve($id) {
        $response = [
            'status' => 'success',
            'message' => '调拨单已审核通过',
            'data' => [
                'id' => $id,
                'status' => 'approved',
                'approve_time' => date('Y-m-d H:i:s')
            ]
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 完成库存调拨
     */
    public function transfer_complete($id) {
        $response = [
            'status' => 'success',
            'message' => '调拨单已完成',
            'data' => [
                'id' => $id,
                'status' => 'completed',
                'complete_time' => date('Y-m-d H:i:s')
            ]
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }

    /**
     * 取消库存调拨单
     */
    public function transfer_cancel($id) {
        $response = [
            'status' => 'success',
            'message' => '调拨单已取消',
            'data' => [
                'id' => $id,
                'status' => 'cancelled',
                'cancel_time' => date('Y-m-d H:i:s')
            ]
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
    
    /**
     * 获取库存项目列表
     */
    public function items() {
        // 获取查询参数
        $params = [
            'page' => $this->input->get('page') ? intval($this->input->get('page')) : 1,
            'limit' => $this->input->get('limit') ? intval($this->input->get('limit')) : 10,
            'keyword' => $this->input->get('keyword'),
            'warehouse_id' => $this->input->get('warehouse_id') ? intval($this->input->get('warehouse_id')) : null,
            'category_id' => $this->input->get('category_id') ? intval($this->input->get('category_id')) : null,
            'status' => $this->input->get('status')
        ];
        
        // 从数据库获取库存项目列表
        $items_data = $this->inventory_model->get_stock_list($params);
        $total = $this->inventory_model->count_stock_list($params);
        
        // 整理数据格式
        $items = [];
        foreach ($items_data as $item) {
            $items[] = [
                'id' => $item['id'],
                'product_id' => $item['product_id'],
                'product_code' => $item['product_code'],
                'product_name' => $item['product_name'],
                'warehouse_id' => $item['warehouse_id'],
                'warehouse_name' => $item['warehouse_name'],
                'quantity' => $item['quantity'],
                'unit' => $item['unit'],
                'status' => $item['status']
            ];
        }
        
        // 返回数据
        $data = [
            'list' => $items,
            'total' => $total
        ];
        
        $this->output
            ->set_content_type('application/json')
            ->set_output(json_encode(['status' => true, 'data' => $data], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
