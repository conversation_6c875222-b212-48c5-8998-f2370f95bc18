<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 产品模型类
 * 处理产品相关的数据库操作
 */
class Product_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取产品列表
     * @param array $params 查询参数
     * @return array 产品列表
     */
    public function get_products($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'p.category_id = c.id', 'left');
        $this->db->where('p.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('p.name', $keyword);
            $this->db->or_like('p.code', $keyword);
            $this->db->or_like('p.specification', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('p.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('p.status', $params['status']);
        }
        
        $this->db->order_by('p.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取产品总数
     * @param array $params 查询参数
     * @return int 产品总数
     */
    public function count_products($params = []) {
        $this->db->from('products p');
        $this->db->where('p.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('p.name', $keyword);
            $this->db->or_like('p.code', $keyword);
            $this->db->or_like('p.specification', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('p.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('p.status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取产品信息
     * @param int $id 产品ID
     * @return array|null 产品信息
     */
    public function get_product_by_id($id) {
        $this->db->select('p.*, c.name as category_name');
        $this->db->from('products p');
        $this->db->join('categories c', 'p.category_id = c.id', 'left');
        $this->db->where('p.id', $id);
        $this->db->where('p.is_deleted', 0);
        
        $query = $this->db->get();
        $result = $query->row_array();
        
        return $result;
    }
    
    /**
     * 检查产品编码是否存在
     * @param string $code 产品编码
     * @param int $exclude_id 排除的产品ID
     * @return bool 是否存在
     */
    public function is_code_exists($code, $exclude_id = 0) {
        $this->db->from('products');
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id > 0) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 创建产品
     * @param array $data 产品数据
     * @return int|bool 产品ID或false
     */
    public function create_product($data) {
        $this->db->insert('products', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新产品
     * @param int $id 产品ID
     * @param array $data 产品数据
     * @return bool 是否成功
     */
    public function update_product($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('products', $data);
    }
    
    /**
     * 批量更新产品
     * @param array $ids 产品ID数组
     * @param array $data 产品数据
     * @return int 更新的记录数
     */
    public function batch_update_products($ids, $data) {
        $this->db->where_in('id', $ids);
        $this->db->update('products', $data);
        return $this->db->affected_rows();
    }
    
    /**
     * 删除产品（软删除）
     * @param int $id 产品ID
     * @return bool 是否成功
     */
    public function delete_product($id) {
        $this->db->where('id', $id);
        return $this->db->update('products', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 批量删除产品（软删除）
     * @param array $ids 产品ID数组
     * @return int 删除的记录数
     */
    public function batch_delete_products($ids) {
        $this->db->where_in('id', $ids);
        $this->db->update('products', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
        return $this->db->affected_rows();
    }
    
    /**
     * 根据产品ID获取库存信息
     * @param int $id 产品ID
     * @return array 库存信息
     */
    public function get_stock_by_product_id($id) {
        $this->db->select('w.id, w.name as warehouse_name, COALESCE(s.quantity, 0) as quantity');
        $this->db->from('warehouses w');
        $this->db->join('stock s', 'w.id = s.warehouse_id AND s.product_id = ' . $id, 'left');
        $this->db->where('w.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取产品价格历史
     * @param int $id 产品ID
     * @param array $params 查询参数
     * @return array 价格历史
     */
    public function get_price_history($id, $params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('ph.*, u.name as created_by_name');
        $this->db->from('price_history ph');
        $this->db->join('users u', 'ph.created_by = u.id', 'left');
        $this->db->where('ph.product_id', $id);
        
        if (isset($params['type']) && $params['type']) {
            $this->db->where('ph.type', $params['type']);
        }
        
        $this->db->order_by('ph.created_at', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 添加价格历史记录
     * @param array $data 价格历史数据
     * @return bool 是否成功
     */
    public function add_price_history($data) {
        return $this->db->insert('price_history', $data);
    }
}
