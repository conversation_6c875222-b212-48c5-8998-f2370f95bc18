import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login, logout, getUserInfo, changePassword } from '@/api/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref('')
  const userInfo = ref({})
  const permissions = ref([])

  // 登录
  const loginAction = async (username, password, rememberMe = false) => {
    try {
      const res = await login(username, password, rememberMe)
      if (res.status) {
        token.value = res.data.access_token
        userInfo.value = {
          uid: res.data.uid,
          name: res.data.name,
          username: res.data.username,
          role_id: res.data.role_id
        }
        permissions.value = res.data.permissions || []

        // 存储到本地存储
        localStorage.setItem('token', token.value)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
        localStorage.setItem('permissions', JSON.stringify(permissions.value))

        return Promise.resolve(res)
      }
      return Promise.reject(res.message || '登录失败')
    } catch (error) {
      return Promise.reject(error.message || '登录失败')
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await logout()
      }
    } catch (error) {
      console.error('登出接口调用失败:', error)
    } finally {
      // 清除状态和本地存储
      resetState()
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const res = await getUserInfo()
      if (res.status) {
        userInfo.value = res.data
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
        return Promise.resolve(res.data)
      }
      return Promise.reject(res.message || '获取用户信息失败')
    } catch (error) {
      return Promise.reject(error.message || '获取用户信息失败')
    }
  }

  // 修改密码
  const changePasswordAction = async (oldPassword, newPassword, confirmPassword) => {
    try {
      const res = await changePassword(oldPassword, newPassword, confirmPassword)
      if (res.status) {
        return Promise.resolve(res)
      }
      return Promise.reject(res.message || '修改密码失败')
    } catch (error) {
      return Promise.reject(error.message || '修改密码失败')
    }
  }

  // 检查权限
  const hasPermission = (permission) => {
    if (!permission) return true
    if (permissions.value.includes('*')) return true
    return permissions.value.includes(permission)
  }

  // 检查多个权限（任一满足即可）
  const hasAnyPermission = (permissionList) => {
    if (!permissionList || permissionList.length === 0) return true
    if (permissions.value.includes('*')) return true
    return permissionList.some(permission => permissions.value.includes(permission))
  }

  // 检查多个权限（全部满足）
  const hasAllPermissions = (permissionList) => {
    if (!permissionList || permissionList.length === 0) return true
    if (permissions.value.includes('*')) return true
    return permissionList.every(permission => permissions.value.includes(permission))
  }

  // 恢复会话
  const restoreSession = () => {
    const storedToken = localStorage.getItem('token')
    const storedUserInfo = localStorage.getItem('userInfo')
    const storedPermissions = localStorage.getItem('permissions')

    if (storedToken) {
      token.value = storedToken

      if (storedUserInfo) {
        try {
          userInfo.value = JSON.parse(storedUserInfo)
        } catch (error) {
          console.error('解析用户信息失败:', error)
          resetState()
          return false
        }
      }

      if (storedPermissions) {
        try {
          permissions.value = JSON.parse(storedPermissions)
        } catch (error) {
          console.error('解析权限信息失败:', error)
          permissions.value = []
        }
      }

      return true
    }
    return false
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = {}
    permissions.value = []
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
    localStorage.removeItem('permissions')
  }

  return {
    token,
    userInfo,
    permissions,
    loginAction,
    logoutAction,
    getUserInfoAction,
    changePasswordAction,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    restoreSession,
    resetState
  }
})
