<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * APIu57fau7840u63a7u5236u5668
 * 
 * u6240u6709APIu63a7u5236u5668u7684u7236u7c7buff0cu63d0u4f9bu901au7528u7684u54cdu5e94u683cu5f0fu548cu9a8cu8bc1u65b9u6cd5
 */
class Api extends CI_Controller {
    
    protected $response = [];
    protected $status_code = 200;
    protected $request_data = [];
    protected $user_id = 0;
    protected $token = '';
    
    public function __construct() {
        parent::__construct();
        
        // u52a0u8f7du5fc5u8981u7684u7c7bu5e93
        $this->load->database();
        $this->load->library('session');
        $this->load->helper('url');
        
        // u83b7u53d6u8bf7u6c42u6570u636e
        $this->_get_request_data();
        
        // u9a8cu8bc1APIu8bf7u6c42uff08u9664u4e86u767bu5f55u7b49u5f00u653eu63a5u53e3uff09
        $excluded_routes = ['auth/login', 'auth/register'];
        if (!in_array($this->router->fetch_class() . '/' . $this->router->fetch_method(), $excluded_routes)) {
            $this->_check_token();
        }
    }
    
    /**
     * u83b7u53d6u8bf7u6c42u6570u636e
     */
    private function _get_request_data() {
        $method = $this->input->server('REQUEST_METHOD');
        
        switch ($method) {
            case 'GET':
                $this->request_data = $this->input->get();
                break;
            case 'POST':
                $this->request_data = $this->input->post();
                if (empty($this->request_data)) {
                    $this->request_data = json_decode(file_get_contents('php://input'), true);
                }
                break;
            case 'PUT':
            case 'DELETE':
                $this->request_data = json_decode(file_get_contents('php://input'), true);
                break;
            default:
                $this->request_data = [];
        }
    }
    
    /**
     * u9a8cu8bc1u8bbfu95eeToken
     */
    private function _check_token() {
        // u4eceu8bf7u6c42u5934u83b7u53d6Token
        $this->token = $this->input->get_request_header('Authorization', TRUE);
        
        if (empty($this->token)) {
            $this->response(['status' => false, 'message' => 'u672au63d0u4f9bu6388u6743u4ee4u724c'], 401);
            return;
        }
        
        // u79fbu9664Beareru524du7f00
        if (strpos($this->token, 'Bearer ') === 0) {
            $this->token = substr($this->token, 7);
        }
        
        // u9a8cu8bc1Tokenu5e76u83b7u53d6u7528u6237u4fe1u606f
        $this->load->model('auth_model');
        $user = $this->auth_model->validate_token($this->token);
        
        if (!$user) {
            $this->response(['status' => false, 'message' => 'u65e0u6548u7684u8bbfu95eeu4ee4u724cu6216u4ee4u724cu5df2u8fc7u671f'], 401);
            return;
        }
        
        $this->user_id = $user['uid'];
    }
    
    /**
     * u53d1u9001APIu54cdu5e94
     * 
     * @param array $data u54cdu5e94u6570u636e
     * @param int $status_code HTTPu72b6u6001u7801
     */
    protected function response($data, $status_code = NULL) {
        if ($status_code !== NULL) {
            $this->status_code = $status_code;
        }
        
        // u8bbeu7f6eu54cdu5e94u5934
        $this->output
            ->set_content_type('application/json', 'utf-8')
            ->set_status_header($this->status_code)
            ->set_output(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE))
            ->_display();
        exit;
    }
    
    /**
     * u53d1u9001u6210u529fu54cdu5e94
     * 
     * @param mixed $data u8981u8fd4u56deu7684u6570u636e
     * @param string $message u6210u529fu6d88u606f
     */
    protected function success($data = [], $message = 'u64cdu4f5cu6210u529f') {
        $this->response([
            'status' => true,
            'message' => $message,
            'data' => $data
        ]);
    }
    
    /**
     * u53d1u9001u9519u8befu54cdu5e94
     * 
     * @param string $message u9519u8befu6d88u606f
     * @param int $status_code HTTPu72b6u6001u7801
     */
    protected function error($message = 'u64cdu4f5cu5931u8d25', $status_code = 400) {
        $this->response([
            'status' => false,
            'message' => $message
        ], $status_code);
    }
}
