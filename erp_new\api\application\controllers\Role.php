<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 角色管理控制器
 * 实现角色相关的API接口
 */
class Role extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('role_model');
    }

    /**
     * 获取角色列表
     * GET /api/roles
     */
    public function index_get() {
        // 检查权限
        if (!$this->has_permission('role_view')) {
            $this->response([
                'status' => false,
                'message' => '无权限查看角色列表'
            ], 403);
            return;
        }

        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'status' => $this->get('status', true)
        ];

        // 获取角色列表和总数
        $roles = $this->role_model->get_roles($params);
        $total = $this->role_model->count_roles($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $roles,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取所有角色（用于下拉列表）
     * GET /api/roles/all
     */
    public function all_get() {
        // 获取所有角色
        $roles = $this->role_model->get_all_roles();

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $roles
        ], 200);
    }

    /**
     * 获取单个角色详情
     * GET /api/roles/:id
     */
    public function detail_get($id) {
        // 检查权限
        if (!$this->has_permission('role_view')) {
            $this->response([
                'status' => false,
                'message' => '无权限查看角色详情'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '角色ID不能为空'
            ], 400);
            return;
        }

        // 获取角色详情
        $role = $this->role_model->get_role_by_id($id);
        if (!$role) {
            $this->response([
                'status' => false,
                'message' => '角色不存在'
            ], 404);
            return;
        }

        // 获取角色权限
        $permissions = $this->role_model->get_role_permissions($id);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'role' => $role,
                'permissions' => $permissions
            ]
        ], 200);
    }

    /**
     * 创建角色
     * POST /api/roles
     */
    public function create_post() {
        // 检查权限
        if (!$this->has_permission('role_add')) {
            $this->response([
                'status' => false,
                'message' => '无权限创建角色'
            ], 403);
            return;
        }

        // 获取请求数据
        $data = [
            'name' => $this->post('name', true),
            'description' => $this->post('description', true),
            'status' => $this->post('status', true) ? $this->post('status', true) : 'active',
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 获取权限数据
        $permissions = $this->post('permissions');

        // 验证必填字段
        if (!$data['name']) {
            $this->response([
                'status' => false,
                'message' => '角色名称不能为空'
            ], 400);
            return;
        }

        // 验证角色名称唯一性
        if ($this->role_model->check_name_exists($data['name'])) {
            $this->response([
                'status' => false,
                'message' => '角色名称已存在'
            ], 400);
            return;
        }

        // 创建角色
        $role_id = $this->role_model->create_role($data);
        if (!$role_id) {
            $this->response([
                'status' => false,
                'message' => '创建角色失败'
            ], 500);
            return;
        }

        // 更新角色权限
        if (is_array($permissions) && count($permissions) > 0) {
            $this->role_model->update_role_permissions($role_id, $permissions);
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建角色成功',
            'data' => [
                'id' => $role_id
            ]
        ], 201);
    }

    /**
     * 更新角色
     * PUT /api/roles/:id
     */
    public function update_put($id) {
        // 检查权限
        if (!$this->has_permission('role_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新角色'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '角色ID不能为空'
            ], 400);
            return;
        }

        // 检查角色是否存在
        $role = $this->role_model->get_role_by_id($id);
        if (!$role) {
            $this->response([
                'status' => false,
                'message' => '角色不存在'
            ], 404);
            return;
        }

        // 不能修改超级管理员角色
        if ($id == 1) {
            $this->response([
                'status' => false,
                'message' => '不能修改超级管理员角色'
            ], 400);
            return;
        }

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'description' => $this->put('description', true),
            'status' => $this->put('status', true),
            'updated_by' => $this->user_id,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 获取权限数据
        $permissions = $this->put('permissions');

        // 验证必填字段
        if (isset($data['name']) && !$data['name']) {
            $this->response([
                'status' => false,
                'message' => '角色名称不能为空'
            ], 400);
            return;
        }

        // 验证角色名称唯一性
        if (isset($data['name']) && $data['name'] != $role['name'] && $this->role_model->check_name_exists($data['name'])) {
            $this->response([
                'status' => false,
                'message' => '角色名称已存在'
            ], 400);
            return;
        }

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 更新角色
        $result = $this->role_model->update_role($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新角色失败'
            ], 500);
            return;
        }

        // 更新角色权限
        if (is_array($permissions)) {
            $this->role_model->update_role_permissions($id, $permissions);
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新角色成功'
        ], 200);
    }

    /**
     * 删除角色
     * DELETE /api/roles/:id
     */
    public function delete_delete($id) {
        // 检查权限
        if (!$this->has_permission('role_delete')) {
            $this->response([
                'status' => false,
                'message' => '无权限删除角色'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '角色ID不能为空'
            ], 400);
            return;
        }

        // 检查角色是否存在
        $role = $this->role_model->get_role_by_id($id);
        if (!$role) {
            $this->response([
                'status' => false,
                'message' => '角色不存在'
            ], 404);
            return;
        }

        // 不能删除超级管理员角色
        if ($id == 1) {
            $this->response([
                'status' => false,
                'message' => '不能删除超级管理员角色'
            ], 400);
            return;
        }

        // 检查角色是否被引用
        if ($this->role_model->check_role_in_use($id)) {
            $this->response([
                'status' => false,
                'message' => '该角色已被用户引用，无法删除'
            ], 400);
            return;
        }

        // 删除角色
        $result = $this->role_model->delete_role($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除角色失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除角色成功'
        ], 200);
    }

    /**
     * 获取所有权限
     * GET /api/roles/permissions
     */
    public function permissions_get() {
        // 获取所有权限
        $permissions = $this->role_model->get_all_permissions();

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $permissions
        ], 200);
    }
}
