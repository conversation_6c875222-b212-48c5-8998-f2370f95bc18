<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 供应商模型类
 * 处理供应商相关的数据库操作
 */
class Supplier_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取供应商列表
     * @param array $params 查询参数
     * @return array 供应商列表
     */
    public function get_suppliers($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('s.*, cat.name as category_name');
        $this->db->from('suppliers s');
        $this->db->join('categories cat', 's.category_id = cat.id', 'left');
        $this->db->where('s.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('s.name', $keyword);
            $this->db->or_like('s.code', $keyword);
            $this->db->or_like('s.contact_name', $keyword);
            $this->db->or_like('s.contact_phone', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('s.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('s.status', $params['status']);
        }
        
        $this->db->order_by('s.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取供应商总数
     * @param array $params 查询参数
     * @return int 供应商总数
     */
    public function count_suppliers($params = []) {
        $this->db->from('suppliers s');
        $this->db->where('s.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('s.name', $keyword);
            $this->db->or_like('s.code', $keyword);
            $this->db->or_like('s.contact_name', $keyword);
            $this->db->or_like('s.contact_phone', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('s.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('s.status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取供应商信息
     * @param int $id 供应商ID
     * @return array|null 供应商信息
     */
    public function get_supplier_by_id($id) {
        $this->db->select('s.*, cat.name as category_name');
        $this->db->from('suppliers s');
        $this->db->join('categories cat', 's.category_id = cat.id', 'left');
        $this->db->where('s.id', $id);
        $this->db->where('s.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据编码获取供应商信息
     * @param string $code 供应商编码
     * @return array|null 供应商信息
     */
    public function get_supplier_by_code($code) {
        $this->db->from('suppliers');
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 检查供应商编码是否存在
     * @param string $code 供应商编码
     * @param int $exclude_id 排除的供应商ID
     * @return bool 是否存在
     */
    public function is_code_exists($code, $exclude_id = 0) {
        $this->db->from('suppliers');
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id > 0) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 创建供应商
     * @param array $data 供应商数据
     * @return int|bool 供应商ID或false
     */
    public function create_supplier($data) {
        $this->db->insert('suppliers', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新供应商
     * @param int $id 供应商ID
     * @param array $data 供应商数据
     * @return bool 是否成功
     */
    public function update_supplier($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('suppliers', $data);
    }
    
    /**
     * 删除供应商（软删除）
     * @param int $id 供应商ID
     * @return bool 是否成功
     */
    public function delete_supplier($id) {
        $this->db->where('id', $id);
        return $this->db->update('suppliers', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 检查供应商是否被引用
     * @param int $id 供应商ID
     * @return bool 是否被引用
     */
    public function check_supplier_in_use($id) {
        // 检查采购订单
        $this->db->from('purchase_orders');
        $this->db->where('supplier_id', $id);
        $this->db->where('is_deleted', 0);
        if ($this->db->count_all_results() > 0) {
            return true;
        }
        
        // 检查采购入库单
        $this->db->from('purchase_receives');
        $this->db->where('supplier_id', $id);
        $this->db->where('is_deleted', 0);
        if ($this->db->count_all_results() > 0) {
            return true;
        }
        
        // 检查付款记录
        $this->db->from('payments');
        $this->db->where('supplier_id', $id);
        $this->db->where('is_deleted', 0);
        if ($this->db->count_all_results() > 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取供应商应付账款
     * @param int $id 供应商ID
     * @return array 应付账款信息
     */
    public function get_supplier_payables($id) {
        $this->db->select('SUM(grand_total) as total_amount');
        $this->db->from('purchase_orders');
        $this->db->where('supplier_id', $id);
        $this->db->where('status', 'completed');
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        $total_purchases = $query->row_array()['total_amount'] ?: 0;
        
        $this->db->select('SUM(amount) as total_amount');
        $this->db->from('payments');
        $this->db->where('supplier_id', $id);
        $this->db->where('status', 'confirmed');
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        $total_payments = $query->row_array()['total_amount'] ?: 0;
        
        return [
            'total_purchases' => $total_purchases,
            'total_payments' => $total_payments,
            'balance' => $total_purchases - $total_payments
        ];
    }
}
