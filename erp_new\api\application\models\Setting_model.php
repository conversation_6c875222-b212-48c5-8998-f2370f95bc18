<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 系统设置模型类
 * 处理系统设置相关的数据库操作
 */
class Setting_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取所有系统设置
     * @return array 所有系统设置
     */
    public function get_all_settings() {
        $this->db->select('*');
        $this->db->from('settings');
        $this->db->order_by('group', 'ASC');
        $this->db->order_by('key', 'ASC');
        
        $query = $this->db->get();
        $settings = $query->result_array();
        
        // 按分组重组数据
        $result = [];
        foreach ($settings as $setting) {
            $group = $setting['group'];
            $key = $setting['key'];
            $value = $setting['value'];
            
            if (!isset($result[$group])) {
                $result[$group] = [];
            }
            
            $result[$group][$key] = $value;
        }
        
        return $result;
    }
    
    /**
     * 获取指定分组的系统设置
     * @param string $group 分组名称
     * @return array 指定分组的系统设置
     */
    public function get_settings_by_group($group) {
        $this->db->select('`key`, value');
        $this->db->from('settings');
        $this->db->where('group', $group);
        $this->db->order_by('key', 'ASC');
        
        $query = $this->db->get();
        $settings = $query->result_array();
        
        // 重组数据为键值对
        $result = [];
        foreach ($settings as $setting) {
            $key = $setting['key'];
            $value = $setting['value'];
            $result[$key] = $value;
        }
        
        return $result;
    }
    
    /**
     * 获取单个系统设置
     * @param string $group 分组名称
     * @param string $key 设置键名
     * @return string|null 设置值
     */
    public function get_setting($group, $key) {
        $this->db->select('value');
        $this->db->from('settings');
        $this->db->where('group', $group);
        $this->db->where('key', $key);
        
        $query = $this->db->get();
        $setting = $query->row_array();
        
        return $setting ? $setting['value'] : null;
    }
    
    /**
     * 更新系统设置
     * @param string $group 分组名称
     * @param array $settings 设置数据
     * @return bool 是否成功
     */
    public function update_settings($group, $settings) {
        $this->db->trans_start();
        
        foreach ($settings as $key => $value) {
            $this->update_setting($group, $key, $value);
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 更新单个系统设置
     * @param string $group 分组名称
     * @param string $key 设置键名
     * @param string $value 设置值
     * @return bool 是否成功
     */
    public function update_setting($group, $key, $value) {
        // 检查设置是否存在
        $this->db->from('settings');
        $this->db->where('group', $group);
        $this->db->where('key', $key);
        
        $query = $this->db->get();
        
        if ($query->num_rows() > 0) {
            // 更新设置
            $this->db->where('group', $group);
            $this->db->where('key', $key);
            $this->db->update('settings', ['value' => $value, 'updated_at' => date('Y-m-d H:i:s')]);
        } else {
            // 创建设置
            $this->db->insert('settings', [
                'group' => $group,
                'key' => $key,
                'value' => $value,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        return $this->db->affected_rows() > 0;
    }
    
    /**
     * 获取所有数据字典
     * @return array 数据字典
     */
    public function get_all_dictionaries() {
        $this->db->select('*');
        $this->db->from('dictionaries');
        $this->db->where('is_deleted', 0);
        $this->db->order_by('type', 'ASC');
        $this->db->order_by('order', 'ASC');
        
        $query = $this->db->get();
        $dictionaries = $query->result_array();
        
        // 按类型分组
        $result = [];
        foreach ($dictionaries as $dictionary) {
            $type = $dictionary['type'];
            
            if (!isset($result[$type])) {
                $result[$type] = [];
            }
            
            $result[$type][] = $dictionary;
        }
        
        return $result;
    }
    
    /**
     * 获取指定类型的数据字典
     * @param string $type 字典类型
     * @return array 指定类型的数据字典
     */
    public function get_dictionaries_by_type($type) {
        $this->db->select('*');
        $this->db->from('dictionaries');
        $this->db->where('type', $type);
        $this->db->where('is_deleted', 0);
        $this->db->order_by('order', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 根据ID获取数据字典
     * @param int $id 字典ID
     * @return array|null 数据字典
     */
    public function get_dictionary_by_id($id) {
        $this->db->select('*');
        $this->db->from('dictionaries');
        $this->db->where('id', $id);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据类型和编码获取数据字典
     * @param string $type 字典类型
     * @param string $code 字典编码
     * @return array|null 数据字典
     */
    public function get_dictionary_by_code($type, $code) {
        $this->db->select('*');
        $this->db->from('dictionaries');
        $this->db->where('type', $type);
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 检查数据字典是否存在
     * @param string $type 字典类型
     * @param string $code 字典编码
     * @param int $exclude_id 排除的字典ID
     * @return bool 是否存在
     */
    public function check_dictionary_exists($type, $code, $exclude_id = 0) {
        $this->db->from('dictionaries');
        $this->db->where('type', $type);
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id > 0) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 创建数据字典
     * @param array $data 字典数据
     * @return int|bool 字典ID或false
     */
    public function create_dictionary($data) {
        $this->db->insert('dictionaries', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新数据字典
     * @param int $id 字典ID
     * @param array $data 字典数据
     * @return bool 是否成功
     */
    public function update_dictionary($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('dictionaries', $data);
    }
    
    /**
     * 删除数据字典（软删除）
     * @param int $id 字典ID
     * @return bool 是否成功
     */
    public function delete_dictionary($id) {
        $this->db->where('id', $id);
        return $this->db->update('dictionaries', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
}
