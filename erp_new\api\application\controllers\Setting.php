<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 系统设置控制器
 * 实现系统设置相关的API接口
 */
class Setting extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('setting_model');
    }

    /**
     * 获取所有系统设置
     * GET /api/settings
     */
    public function index_get() {
        // 检查权限
        if (!$this->has_permission('setting_view')) {
            $this->response([
                'status' => false,
                'message' => '无权限查看系统设置'
            ], 403);
            return;
        }

        // 获取系统设置
        $settings = $this->setting_model->get_all_settings();

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $settings
        ], 200);
    }

    /**
     * 获取指定分组的系统设置
     * GET /api/settings/:group
     */
    public function group_get($group) {
        // 检查权限
        if (!$this->has_permission('setting_view')) {
            $this->response([
                'status' => false,
                'message' => '无权限查看系统设置'
            ], 403);
            return;
        }

        if (!$group) {
            $this->response([
                'status' => false,
                'message' => '分组名称不能为空'
            ], 400);
            return;
        }

        // 获取系统设置
        $settings = $this->setting_model->get_settings_by_group($group);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $settings
        ], 200);
    }

    /**
     * 更新系统设置
     * PUT /api/settings/:group
     */
    public function update_put($group) {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新系统设置'
            ], 403);
            return;
        }

        if (!$group) {
            $this->response([
                'status' => false,
                'message' => '分组名称不能为空'
            ], 400);
            return;
        }

        // 获取请求数据
        $settings = $this->put('settings');
        if (!is_array($settings)) {
            $this->response([
                'status' => false,
                'message' => '设置数据格式不正确'
            ], 400);
            return;
        }

        // 更新系统设置
        $result = $this->setting_model->update_settings($group, $settings);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新系统设置失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新系统设置成功'
        ], 200);
    }

    /**
     * 获取公司信息
     * GET /api/settings/company
     */
    public function company_get() {
        // 获取公司设置
        $company = $this->setting_model->get_settings_by_group('company');

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $company
        ], 200);
    }

    /**
     * 更新公司信息
     * PUT /api/settings/company
     */
    public function company_put() {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新公司信息'
            ], 403);
            return;
        }

        // 获取请求数据
        $settings = $this->put('settings');
        if (!is_array($settings)) {
            $this->response([
                'status' => false,
                'message' => '设置数据格式不正确'
            ], 400);
            return;
        }

        // 更新公司信息
        $result = $this->setting_model->update_settings('company', $settings);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新公司信息失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新公司信息成功'
        ], 200);
    }

    /**
     * 上传公司LOGO
     * POST /api/settings/logo
     */
    public function logo_post() {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限上传公司LOGO'
            ], 403);
            return;
        }

        // 检查上传目录
        $upload_dir = './uploads/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // 配置上传参数
        $config['upload_path'] = $upload_dir;
        $config['allowed_types'] = 'gif|jpg|jpeg|png';
        $config['max_size'] = 2048; // 2MB
        $config['file_name'] = 'company_logo_' . time();

        $this->load->library('upload', $config);

        // 执行上传
        if (!$this->upload->do_upload('logo')) {
            $error = $this->upload->display_errors('', '');
            $this->response([
                'status' => false,
                'message' => $error
            ], 400);
            return;
        }

        // 获取上传文件信息
        $upload_data = $this->upload->data();
        $logo_path = 'uploads/' . $upload_data['file_name'];

        // 更新公司LOGO设置
        $result = $this->setting_model->update_setting('company', 'logo', $logo_path);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新公司LOGO失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '上传公司LOGO成功',
            'data' => [
                'logo' => base_url($logo_path)
            ]
        ], 200);
    }

    /**
     * 获取系统参数
     * GET /api/settings/parameters
     */
    public function parameters_get() {
        // 获取系统参数设置
        $parameters = $this->setting_model->get_settings_by_group('parameters');

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $parameters
        ], 200);
    }

    /**
     * 更新系统参数
     * PUT /api/settings/parameters
     */
    public function parameters_put() {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新系统参数'
            ], 403);
            return;
        }

        // 获取请求数据
        $settings = $this->put('settings');
        if (!is_array($settings)) {
            $this->response([
                'status' => false,
                'message' => '设置数据格式不正确'
            ], 400);
            return;
        }

        // 更新系统参数
        $result = $this->setting_model->update_settings('parameters', $settings);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新系统参数失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新系统参数成功'
        ], 200);
    }

    /**
     * 获取打印模板
     * GET /api/settings/print-templates
     */
    public function print_templates_get() {
        // 获取打印模板设置
        $templates = $this->setting_model->get_settings_by_group('print_templates');

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $templates
        ], 200);
    }

    /**
     * 更新打印模板
     * PUT /api/settings/print-templates
     */
    public function print_templates_put() {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新打印模板'
            ], 403);
            return;
        }

        // 获取请求数据
        $settings = $this->put('settings');
        if (!is_array($settings)) {
            $this->response([
                'status' => false,
                'message' => '设置数据格式不正确'
            ], 400);
            return;
        }

        // 更新打印模板
        $result = $this->setting_model->update_settings('print_templates', $settings);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新打印模板失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新打印模板成功'
        ], 200);
    }

    /**
     * 获取数据字典
     * GET /api/settings/dictionaries
     */
    public function dictionaries_get() {
        // 获取字典类型
        $type = $this->get('type', true);
        
        // 获取数据字典
        if ($type) {
            $dictionaries = $this->setting_model->get_dictionaries_by_type($type);
        } else {
            $dictionaries = $this->setting_model->get_all_dictionaries();
        }

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $dictionaries
        ], 200);
    }

    /**
     * 创建数据字典
     * POST /api/settings/dictionaries
     */
    public function dictionary_post() {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限创建数据字典'
            ], 403);
            return;
        }

        // 获取请求数据
        $data = [
            'type' => $this->post('type', true),
            'code' => $this->post('code', true),
            'name' => $this->post('name', true),
            'value' => $this->post('value', true),
            'order' => $this->post('order', true) ? $this->post('order', true) : 0,
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$data['type'] || !$data['code'] || !$data['name']) {
            $this->response([
                'status' => false,
                'message' => '类型、编码和名称不能为空'
            ], 400);
            return;
        }

        // 验证编码唯一性
        if ($this->setting_model->check_dictionary_exists($data['type'], $data['code'])) {
            $this->response([
                'status' => false,
                'message' => '该类型下的编码已存在'
            ], 400);
            return;
        }

        // 创建数据字典
        $id = $this->setting_model->create_dictionary($data);
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '创建数据字典失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建数据字典成功',
            'data' => [
                'id' => $id
            ]
        ], 201);
    }

    /**
     * 更新数据字典
     * PUT /api/settings/dictionaries/:id
     */
    public function dictionary_put($id) {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限更新数据字典'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '数据字典ID不能为空'
            ], 400);
            return;
        }

        // 检查数据字典是否存在
        $dictionary = $this->setting_model->get_dictionary_by_id($id);
        if (!$dictionary) {
            $this->response([
                'status' => false,
                'message' => '数据字典不存在'
            ], 404);
            return;
        }

        // 获取请求数据
        $data = [
            'type' => $this->put('type', true),
            'code' => $this->put('code', true),
            'name' => $this->put('name', true),
            'value' => $this->put('value', true),
            'order' => $this->put('order', true),
            'updated_by' => $this->user_id,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (isset($data['type']) && !$data['type']) {
            $this->response([
                'status' => false,
                'message' => '类型不能为空'
            ], 400);
            return;
        }

        if (isset($data['code']) && !$data['code']) {
            $this->response([
                'status' => false,
                'message' => '编码不能为空'
            ], 400);
            return;
        }

        if (isset($data['name']) && !$data['name']) {
            $this->response([
                'status' => false,
                'message' => '名称不能为空'
            ], 400);
            return;
        }

        // 验证编码唯一性
        if (isset($data['type']) && isset($data['code']) && 
            ($data['type'] != $dictionary['type'] || $data['code'] != $dictionary['code']) &&
            $this->setting_model->check_dictionary_exists($data['type'], $data['code'])) {
            $this->response([
                'status' => false,
                'message' => '该类型下的编码已存在'
            ], 400);
            return;
        }

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 更新数据字典
        $result = $this->setting_model->update_dictionary($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新数据字典失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新数据字典成功'
        ], 200);
    }

    /**
     * 删除数据字典
     * DELETE /api/settings/dictionaries/:id
     */
    public function dictionary_delete($id) {
        // 检查权限
        if (!$this->has_permission('setting_edit')) {
            $this->response([
                'status' => false,
                'message' => '无权限删除数据字典'
            ], 403);
            return;
        }

        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '数据字典ID不能为空'
            ], 400);
            return;
        }

        // 检查数据字典是否存在
        $dictionary = $this->setting_model->get_dictionary_by_id($id);
        if (!$dictionary) {
            $this->response([
                'status' => false,
                'message' => '数据字典不存在'
            ], 404);
            return;
        }

        // 删除数据字典
        $result = $this->setting_model->delete_dictionary($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除数据字典失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除数据字典成功'
        ], 200);
    }
}
