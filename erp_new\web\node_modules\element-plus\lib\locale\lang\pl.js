'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var pl = {
  name: "pl",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "<PERSON><PERSON><PERSON><PERSON>\u015B\u0107"
    },
    datepicker: {
      now: "Teraz",
      today: "Dzisiaj",
      cancel: "Anuluj",
      clear: "W<PERSON>czy\u015B\u0107",
      confirm: "OK",
      selectDate: "Wybierz dat\u0119",
      selectTime: "<PERSON><PERSON><PERSON><PERSON> godzin\u0119",
      startDate: "Data pocz\u0105tkowa",
      startTime: "God<PERSON>a pocz\u0105tkowa",
      endDate: "Data ko\u0144cowa",
      endTime: "Czas ko\u0144cowa",
      prevYear: "Poprzedni rok",
      nextYear: "Nast\u0119pny rok",
      prevMonth: "Poprzedni miesi\u0105c",
      nextMonth: "Nast\u0119pny miesi\u0105c",
      year: "rok",
      month1: "stycze\u0144",
      month2: "luty",
      month3: "marzec",
      month4: "kwiecie\u0144",
      month5: "maj",
      month6: "czerwiec",
      month7: "lipiec",
      month8: "sierpie\u0144",
      month9: "wrzesie\u0144",
      month10: "pa\u017Adziernik",
      month11: "listopad",
      month12: "grudzie\u0144",
      week: "tydzie\u0144",
      weeks: {
        sun: "niedz.",
        mon: "pon.",
        tue: "wt.",
        wed: "\u015Br.",
        thu: "czw.",
        fri: "pt.",
        sat: "sob."
      },
      months: {
        jan: "STY",
        feb: "LUT",
        mar: "MAR",
        apr: "KWI",
        may: "MAJ",
        jun: "CZE",
        jul: "LIP",
        aug: "SIE",
        sep: "WRZ",
        oct: "PA\u0179",
        nov: "LIS",
        dec: "GRU"
      }
    },
    select: {
      loading: "\u0141adowanie",
      noMatch: "Brak dopasowa\u0144",
      noData: "Brak danych",
      placeholder: "Wybierz"
    },
    mention: {
      loading: "\u0141adowanie"
    },
    cascader: {
      noMatch: "Brak dopasowa\u0144",
      loading: "\u0141adowanie",
      placeholder: "Wybierz",
      noData: "Brak danych"
    },
    pagination: {
      goto: "Id\u017A do",
      pagesize: "/stron\u0119",
      total: "Wszystkich {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Wiadomo\u015B\u0107",
      confirm: "OK",
      cancel: "Anuluj",
      error: "Wiadomo\u015B\u0107 zawiera niedozwolone znaki"
    },
    upload: {
      deleteTip: "kliknij kasuj aby usun\u0105\u0107",
      delete: "Kasuj",
      preview: "Podgl\u0105d",
      continue: "Kontynuuj"
    },
    table: {
      emptyText: "Brak danych",
      confirmFilter: "Potwierd\u017A",
      resetFilter: "Resetuj",
      clearFilter: "Wszystko",
      sumText: "Razem"
    },
    tour: {
      next: "Dalej",
      previous: "Wr\xF3\u0107",
      finish: "Zako\u0144cz"
    },
    tree: {
      emptyText: "Brak danych"
    },
    transfer: {
      noMatch: "Brak dopasowa\u0144",
      noData: "Brak danych",
      titles: ["Lista 1", "Lista 2"],
      filterPlaceholder: "Wpisz szukan\u0105 fraz\u0119",
      noCheckedFormat: "razem: {total}",
      hasCheckedFormat: "wybranych: {checked}/{total}"
    },
    image: {
      error: "B\u0141\u0104D"
    },
    pageHeader: {
      title: "Wstecz"
    },
    popconfirm: {
      confirmButtonText: "Tak",
      cancelButtonText: "Nie"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

exports["default"] = pl;
//# sourceMappingURL=pl.js.map
