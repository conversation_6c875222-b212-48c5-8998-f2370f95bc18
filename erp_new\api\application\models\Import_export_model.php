<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 导入导出模型类
 * 处理Excel导入导出相关的操作
 */
class Import_export_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->model('product_model');
        $this->load->model('customer_model');
        $this->load->model('supplier_model');
        $this->load->model('category_model');
    }

    /**
     * 导出产品数据到Excel
     * @param array $products 产品数据
     * @return string|bool Excel文件路径或false
     */
    public function export_products($products) {
        try {
            // 创建Excel对象
            $objPHPExcel = new PHPExcel();
            
            // 设置文档属性
            $objPHPExcel->getProperties()
                ->setCreator("ERP System")
                ->setLastModifiedBy("ERP System")
                ->setTitle("产品数据")
                ->setSubject("产品数据导出")
                ->setDescription("产品数据导出文件");
            
            // 设置当前工作表
            $objPHPExcel->setActiveSheetIndex(0);
            $sheet = $objPHPExcel->getActiveSheet();
            $sheet->setTitle('产品数据');
            
            // 设置表头
            $headers = [
                'A' => '产品编码',
                'B' => '产品名称',
                'C' => '分类',
                'D' => '规格型号',
                'E' => '单位',
                'F' => '采购价格',
                'G' => '销售价格',
                'H' => '税率(%)',
                'I' => '库存预警值',
                'J' => '状态',
                'K' => '备注'
            ];
            
            foreach ($headers as $column => $header) {
                $sheet->setCellValue($column . '1', $header);
            }
            
            // 填充数据
            $row = 2;
            foreach ($products as $product) {
                $sheet->setCellValue('A' . $row, $product['code']);
                $sheet->setCellValue('B' . $row, $product['name']);
                $sheet->setCellValue('C' . $row, $product['category_name']);
                $sheet->setCellValue('D' . $row, $product['specification']);
                $sheet->setCellValue('E' . $row, $product['unit']);
                $sheet->setCellValue('F' . $row, $product['purchase_price']);
                $sheet->setCellValue('G' . $row, $product['selling_price']);
                $sheet->setCellValue('H' . $row, $product['tax_rate']);
                $sheet->setCellValue('I' . $row, $product['stock_warning']);
                $sheet->setCellValue('J' . $row, $product['status']);
                $sheet->setCellValue('K' . $row, $product['remark']);
                $row++;
            }
            
            // 设置列宽
            foreach (range('A', 'K') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }
            
            // 设置表头样式
            $headerStyle = [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'type' => PHPExcel_Style_Fill::FILL_SOLID,
                    'color' => ['rgb' => '4F81BD']
                ],
                'alignment' => [
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER
                ]
            ];
            
            $sheet->getStyle('A1:K1')->applyFromArray($headerStyle);
            
            // 创建保存目录
            $dir = './uploads/export/';
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
            
            // 保存Excel文件
            $file_name = 'products_' . date('YmdHis') . '.xlsx';
            $file_path = $dir . $file_name;
            $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
            $objWriter->save($file_path);
            
            return 'uploads/export/' . $file_name;
        } catch (Exception $e) {
            log_message('error', 'Export products error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 导入产品数据
     * @param string $file_path Excel文件路径
     * @return array 导入结果
     */
    public function import_products($file_path) {
        try {
            // 加载Excel文件
            $objPHPExcel = PHPExcel_IOFactory::load($file_path);
            $sheet = $objPHPExcel->getActiveSheet();
            
            // 获取总行数
            $highestRow = $sheet->getHighestRow();
            
            // 验证表头
            $headers = [
                'A' => '产品编码',
                'B' => '产品名称',
                'C' => '分类',
                'D' => '规格型号',
                'E' => '单位',
                'F' => '采购价格',
                'G' => '销售价格',
                'H' => '税率(%)',
                'I' => '库存预警值',
                'J' => '状态',
                'K' => '备注'
            ];
            
            foreach ($headers as $column => $header) {
                if ($sheet->getCell($column . '1')->getValue() !== $header) {
                    return [
                        'success' => false,
                        'message' => '表头格式不正确，请使用导出的模板'
                    ];
                }
            }
            
            // 开始导入数据
            $imported = 0;
            $failed = 0;
            $errors = [];
            
            for ($row = 2; $row <= $highestRow; $row++) {
                $code = $sheet->getCell('A' . $row)->getValue();
                $name = $sheet->getCell('B' . $row)->getValue();
                $category_name = $sheet->getCell('C' . $row)->getValue();
                $specification = $sheet->getCell('D' . $row)->getValue();
                $unit = $sheet->getCell('E' . $row)->getValue();
                $purchase_price = $sheet->getCell('F' . $row)->getValue();
                $selling_price = $sheet->getCell('G' . $row)->getValue();
                $tax_rate = $sheet->getCell('H' . $row)->getValue();
                $stock_warning = $sheet->getCell('I' . $row)->getValue();
                $status = $sheet->getCell('J' . $row)->getValue();
                $remark = $sheet->getCell('K' . $row)->getValue();
                
                // 验证必填字段
                if (!$name) {
                    $errors[] = "第{$row}行：产品名称不能为空";
                    $failed++;
                    continue;
                }
                
                // 验证分类
                $category_id = 0;
                if ($category_name) {
                    $category = $this->category_model->get_category_by_name('product', $category_name);
                    if ($category) {
                        $category_id = $category['id'];
                    } else {
                        $errors[] = "第{$row}行：分类「{$category_name}」不存在";
                        $failed++;
                        continue;
                    }
                }
                
                // 验证产品编码唯一性
                if ($code && $this->product_model->is_code_exists($code)) {
                    $errors[] = "第{$row}行：产品编码「{$code}」已存在";
                    $failed++;
                    continue;
                }
                
                // 准备产品数据
                $product_data = [
                    'code' => $code ? $code : 'P' . date('YmdHis') . rand(100, 999),
                    'name' => $name,
                    'category_id' => $category_id,
                    'specification' => $specification,
                    'unit' => $unit,
                    'purchase_price' => $purchase_price ? $purchase_price : 0,
                    'selling_price' => $selling_price ? $selling_price : 0,
                    'tax_rate' => $tax_rate ? $tax_rate : 0,
                    'stock_warning' => $stock_warning ? $stock_warning : 0,
                    'status' => $status ? $status : 'active',
                    'remark' => $remark,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                // 创建产品
                $product_id = $this->product_model->create_product($product_data);
                if ($product_id) {
                    $imported++;
                } else {
                    $errors[] = "第{$row}行：产品「{$name}」导入失败";
                    $failed++;
                }
            }
            
            return [
                'success' => true,
                'imported' => $imported,
                'failed' => $failed,
                'errors' => $errors
            ];
        } catch (Exception $e) {
            log_message('error', 'Import products error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '导入产品数据失败：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 导出客户数据到Excel
     * @param array $customers 客户数据
     * @return string|bool Excel文件路径或false
     */
    public function export_customers($customers) {
        try {
            // 创建Excel对象
            $objPHPExcel = new PHPExcel();
            
            // 设置文档属性
            $objPHPExcel->getProperties()
                ->setCreator("ERP System")
                ->setLastModifiedBy("ERP System")
                ->setTitle("客户数据")
                ->setSubject("客户数据导出")
                ->setDescription("客户数据导出文件");
            
            // 设置当前工作表
            $objPHPExcel->setActiveSheetIndex(0);
            $sheet = $objPHPExcel->getActiveSheet();
            $sheet->setTitle('客户数据');
            
            // 设置表头
            $headers = [
                'A' => '客户编码',
                'B' => '客户名称',
                'C' => '分类',
                'D' => '联系人',
                'E' => '联系电话',
                'F' => '联系邮箱',
                'G' => '地址',
                'H' => '税号',
                'I' => '开户银行',
                'J' => '银行账号',
                'K' => '信用额度',
                'L' => '状态',
                'M' => '备注'
            ];
            
            foreach ($headers as $column => $header) {
                $sheet->setCellValue($column . '1', $header);
            }
            
            // 填充数据
            $row = 2;
            foreach ($customers as $customer) {
                $sheet->setCellValue('A' . $row, $customer['code']);
                $sheet->setCellValue('B' . $row, $customer['name']);
                $sheet->setCellValue('C' . $row, $customer['category_name']);
                $sheet->setCellValue('D' . $row, $customer['contact_name']);
                $sheet->setCellValue('E' . $row, $customer['contact_phone']);
                $sheet->setCellValue('F' . $row, $customer['contact_email']);
                $sheet->setCellValue('G' . $row, $customer['address']);
                $sheet->setCellValue('H' . $row, $customer['tax_number']);
                $sheet->setCellValue('I' . $row, $customer['bank_name']);
                $sheet->setCellValue('J' . $row, $customer['bank_account']);
                $sheet->setCellValue('K' . $row, $customer['credit_limit']);
                $sheet->setCellValue('L' . $row, $customer['status']);
                $sheet->setCellValue('M' . $row, $customer['remark']);
                $row++;
            }
            
            // 设置列宽
            foreach (range('A', 'M') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }
            
            // 设置表头样式
            $headerStyle = [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'type' => PHPExcel_Style_Fill::FILL_SOLID,
                    'color' => ['rgb' => '4F81BD']
                ],
                'alignment' => [
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER
                ]
            ];
            
            $sheet->getStyle('A1:M1')->applyFromArray($headerStyle);
            
            // 创建保存目录
            $dir = './uploads/export/';
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
            
            // 保存Excel文件
            $file_name = 'customers_' . date('YmdHis') . '.xlsx';
            $file_path = $dir . $file_name;
            $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
            $objWriter->save($file_path);
            
            return 'uploads/export/' . $file_name;
        } catch (Exception $e) {
            log_message('error', 'Export customers error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 导入客户数据
     * @param string $file_path Excel文件路径
     * @return array 导入结果
     */
    public function import_customers($file_path) {
        try {
            // 加载Excel文件
            $objPHPExcel = PHPExcel_IOFactory::load($file_path);
            $sheet = $objPHPExcel->getActiveSheet();
            
            // 获取总行数
            $highestRow = $sheet->getHighestRow();
            
            // 验证表头
            $headers = [
                'A' => '客户编码',
                'B' => '客户名称',
                'C' => '分类',
                'D' => '联系人',
                'E' => '联系电话',
                'F' => '联系邮箱',
                'G' => '地址',
                'H' => '税号',
                'I' => '开户银行',
                'J' => '银行账号',
                'K' => '信用额度',
                'L' => '状态',
                'M' => '备注'
            ];
            
            foreach ($headers as $column => $header) {
                if ($sheet->getCell($column . '1')->getValue() !== $header) {
                    return [
                        'success' => false,
                        'message' => '表头格式不正确，请使用导出的模板'
                    ];
                }
            }
            
            // 开始导入数据
            $imported = 0;
            $failed = 0;
            $errors = [];
            
            for ($row = 2; $row <= $highestRow; $row++) {
                $code = $sheet->getCell('A' . $row)->getValue();
                $name = $sheet->getCell('B' . $row)->getValue();
                $category_name = $sheet->getCell('C' . $row)->getValue();
                $contact_name = $sheet->getCell('D' . $row)->getValue();
                $contact_phone = $sheet->getCell('E' . $row)->getValue();
                $contact_email = $sheet->getCell('F' . $row)->getValue();
                $address = $sheet->getCell('G' . $row)->getValue();
                $tax_number = $sheet->getCell('H' . $row)->getValue();
                $bank_name = $sheet->getCell('I' . $row)->getValue();
                $bank_account = $sheet->getCell('J' . $row)->getValue();
                $credit_limit = $sheet->getCell('K' . $row)->getValue();
                $status = $sheet->getCell('L' . $row)->getValue();
                $remark = $sheet->getCell('M' . $row)->getValue();
                
                // 验证必填字段
                if (!$name) {
                    $errors[] = "第{$row}行：客户名称不能为空";
                    $failed++;
                    continue;
                }
                
                // 验证分类
                $category_id = 0;
                if ($category_name) {
                    $category = $this->category_model->get_category_by_name('customer', $category_name);
                    if ($category) {
                        $category_id = $category['id'];
                    } else {
                        $errors[] = "第{$row}行：分类「{$category_name}」不存在";
                        $failed++;
                        continue;
                    }
                }
                
                // 验证客户编码唯一性
                if ($code && $this->customer_model->is_code_exists($code)) {
                    $errors[] = "第{$row}行：客户编码「{$code}」已存在";
                    $failed++;
                    continue;
                }
                
                // 准备客户数据
                $customer_data = [
                    'code' => $code ? $code : 'C' . date('YmdHis') . rand(100, 999),
                    'name' => $name,
                    'category_id' => $category_id,
                    'contact_name' => $contact_name,
                    'contact_phone' => $contact_phone,
                    'contact_email' => $contact_email,
                    'address' => $address,
                    'tax_number' => $tax_number,
                    'bank_name' => $bank_name,
                    'bank_account' => $bank_account,
                    'credit_limit' => $credit_limit ? $credit_limit : 0,
                    'status' => $status ? $status : 'active',
                    'remark' => $remark,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                // 创建客户
                $customer_id = $this->customer_model->create_customer($customer_data);
                if ($customer_id) {
                    $imported++;
                } else {
                    $errors[] = "第{$row}行：客户「{$name}」导入失败";
                    $failed++;
                }
            }
            
            return [
                'success' => true,
                'imported' => $imported,
                'failed' => $failed,
                'errors' => $errors
            ];
        } catch (Exception $e) {
            log_message('error', 'Import customers error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '导入客户数据失败：' . $e->getMessage()
            ];
        }
    }
}
