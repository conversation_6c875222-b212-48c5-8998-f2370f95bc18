<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 分类管理控制器
 * 实现分类相关的API接口
 */
class Category extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('category_model');
    }

    /**
     * 获取分类列表
     * GET /api/categories
     */
    public function index_get() {
        // 获取查询参数
        $type = $this->get('type', true);

        // 获取分类列表
        $categories = $this->category_model->get_categories($type);

        // 构建树形结构
        $tree = $this->build_category_tree($categories);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $tree
        ], 200);
    }

    /**
     * 获取单个分类详情
     * GET /api/categories/:id
     */
    public function detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '分类ID不能为空'
            ], 400);
            return;
        }

        $category = $this->category_model->get_category_by_id($id);
        if (!$category) {
            $this->response([
                'status' => false,
                'message' => '分类不存在'
            ], 404);
            return;
        }

        $this->response([
            'status' => true,
            'data' => $category
        ], 200);
    }

    /**
     * 创建新分类
     * POST /api/categories
     */
    public function create_post() {
        // 验证输入数据
        $this->validate_category_data();

        // 获取请求数据
        $data = [
            'name' => $this->post('name', true),
            'type' => $this->post('type', true),
            'parent_id' => $this->post('parent_id', true) ? $this->post('parent_id', true) : 0,
            'description' => $this->post('description', true),
            'sort_order' => $this->post('sort_order', true) ? $this->post('sort_order', true) : 0,
            'created_by' => $this->get_user_id(),
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 检查分类名称是否已存在
        $existing = $this->category_model->get_category_by_name($data['name'], $data['type']);
        if ($existing) {
            $this->response([
                'status' => false,
                'message' => '分类名称已存在'
            ], 400);
            return;
        }

        // 创建分类
        $category_id = $this->category_model->create_category($data);
        if (!$category_id) {
            $this->response([
                'status' => false,
                'message' => '创建分类失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '创建分类成功',
            'data' => ['id' => $category_id]
        ], 201);
    }

    /**
     * 更新分类
     * PUT /api/categories/:id
     */
    public function update_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '分类ID不能为空'
            ], 400);
            return;
        }

        // 检查分类是否存在
        $category = $this->category_model->get_category_by_id($id);
        if (!$category) {
            $this->response([
                'status' => false,
                'message' => '分类不存在'
            ], 404);
            return;
        }

        // 验证输入数据
        $this->validate_category_data();

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'type' => $this->put('type', true),
            'parent_id' => $this->put('parent_id', true) ? $this->put('parent_id', true) : 0,
            'description' => $this->put('description', true),
            'sort_order' => $this->put('sort_order', true) ? $this->put('sort_order', true) : 0,
            'updated_by' => $this->get_user_id(),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 检查分类名称是否已存在（排除当前分类）
        $existing = $this->category_model->get_category_by_name($data['name'], $data['type']);
        if ($existing && $existing['id'] != $id) {
            $this->response([
                'status' => false,
                'message' => '分类名称已存在'
            ], 400);
            return;
        }

        // 更新分类
        $result = $this->category_model->update_category($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新分类失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '更新分类成功'
        ], 200);
    }

    /**
     * 删除分类
     * DELETE /api/categories/:id
     */
    public function delete_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '分类ID不能为空'
            ], 400);
            return;
        }

        // 检查分类是否存在
        $category = $this->category_model->get_category_by_id($id);
        if (!$category) {
            $this->response([
                'status' => false,
                'message' => '分类不存在'
            ], 404);
            return;
        }

        // 检查是否有子分类
        $children = $this->category_model->get_children_categories($id);
        if (count($children) > 0) {
            $this->response([
                'status' => false,
                'message' => '该分类下有子分类，无法删除'
            ], 400);
            return;
        }

        // 检查是否有相关产品
        if ($category['type'] == 'product') {
            $this->load->model('product_model');
            $product_count = $this->product_model->count_products(['category_id' => $id]);
            if ($product_count > 0) {
                $this->response([
                    'status' => false,
                    'message' => '该分类下有产品，无法删除'
                ], 400);
                return;
            }
        }

        // 删除分类
        $result = $this->category_model->delete_category($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除分类失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '删除分类成功'
        ], 200);
    }

    /**
     * 构建分类树形结构
     * @param array $categories 分类列表
     * @param int $parent_id 父分类ID
     * @return array 树形结构
     */
    private function build_category_tree($categories, $parent_id = 0) {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parent_id) {
                $children = $this->build_category_tree($categories, $category['id']);
                if ($children) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        return $tree;
    }

    /**
     * 验证分类数据
     */
    private function validate_category_data() {
        $this->form_validation->set_data($this->method == 'post' ? $this->post() : $this->put());
        $this->form_validation->set_rules('name', '分类名称', 'required|trim');
        $this->form_validation->set_rules('type', '分类类型', 'required|trim');

        if (!$this->form_validation->run()) {
            $this->response([
                'status' => false,
                'message' => validation_errors()
            ], 400);
            exit;
        }
    }
}
