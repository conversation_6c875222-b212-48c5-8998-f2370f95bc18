<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 财务管理控制器
 * 实现收款、付款、费用及财务报表等功能
 */
class Finance extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('finance_model');
        $this->load->model('customer_model');
        $this->load->model('supplier_model');
        $this->load->model('sales_model');
        $this->load->model('purchase_model');
    }

    /**
     * 获取收款单列表
     * GET /api/finance/receipts
     */
    public function receipts_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'customer_id' => $this->get('customer_id', true),
            'status' => $this->get('status', true),
            'start_date' => $this->get('start_date', true),
            'end_date' => $this->get('end_date', true)
        ];

        // 获取收款单列表和总数
        $receipts = $this->finance_model->get_receipts($params);
        $total = $this->finance_model->count_receipts($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $receipts,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取收款单详情
     * GET /api/finance/receipts/:id
     */
    public function receipt_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '收款单ID不能为空'
            ], 400);
            return;
        }

        // 获取收款单详情
        $receipt = $this->finance_model->get_receipt_by_id($id);
        if (!$receipt) {
            $this->response([
                'status' => false,
                'message' => '收款单不存在'
            ], 404);
            return;
        }

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $receipt
        ], 200);
    }

    /**
     * 创建收款单
     * POST /api/finance/receipts
     */
    public function receipt_post() {
        // 获取请求数据
        $receipt_data = [
            'receipt_no' => $this->post('receipt_no', true),
            'customer_id' => $this->post('customer_id', true),
            'receipt_date' => $this->post('receipt_date', true),
            'amount' => $this->post('amount', true),
            'payment_method' => $this->post('payment_method', true),
            'reference_no' => $this->post('reference_no', true),
            'order_id' => $this->post('order_id', true),
            'status' => 'draft',
            'remark' => $this->post('remark', true),
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$receipt_data['customer_id']) {
            $this->response([
                'status' => false,
                'message' => '客户不能为空'
            ], 400);
            return;
        }

        if (!$receipt_data['receipt_date']) {
            $this->response([
                'status' => false,
                'message' => '收款日期不能为空'
            ], 400);
            return;
        }

        if (!$receipt_data['amount'] || $receipt_data['amount'] <= 0) {
            $this->response([
                'status' => false,
                'message' => '收款金额必须大于0'
            ], 400);
            return;
        }

        if (!$receipt_data['payment_method']) {
            $this->response([
                'status' => false,
                'message' => '支付方式不能为空'
            ], 400);
            return;
        }

        // 验证客户是否存在
        $customer = $this->customer_model->get_customer_by_id($receipt_data['customer_id']);
        if (!$customer) {
            $this->response([
                'status' => false,
                'message' => '客户不存在'
            ], 400);
            return;
        }

        // 如果关联了销售订单，验证订单是否存在
        if ($receipt_data['order_id']) {
            $order = $this->sales_model->get_order_by_id($receipt_data['order_id']);
            if (!$order) {
                $this->response([
                    'status' => false,
                    'message' => '销售订单不存在'
                ], 400);
                return;
            }

            // 验证订单客户是否匹配
            if ($order['customer_id'] != $receipt_data['customer_id']) {
                $this->response([
                    'status' => false,
                    'message' => '收款单客户与销售订单客户不匹配'
                ], 400);
                return;
            }
        }

        // 如果没有提供收款单号，自动生成
        if (empty($receipt_data['receipt_no'])) {
            $receipt_data['receipt_no'] = 'RC' . date('YmdHis');
        }

        // 创建收款单
        $receipt_id = $this->finance_model->create_receipt($receipt_data);
        if (!$receipt_id) {
            $this->response([
                'status' => false,
                'message' => '创建收款单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建收款单成功',
            'data' => [
                'id' => $receipt_id,
                'receipt_no' => $receipt_data['receipt_no']
            ]
        ], 201);
    }

    /**
     * 更新收款单状态
     * PUT /api/finance/receipts/:id/status
     */
    public function receipt_status_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '收款单ID不能为空'
            ], 400);
            return;
        }

        // 获取请求数据
        $status = $this->put('status', true);
        if (!$status) {
            $this->response([
                'status' => false,
                'message' => '状态不能为空'
            ], 400);
            return;
        }

        // 检查收款单是否存在
        $receipt = $this->finance_model->get_receipt_by_id($id);
        if (!$receipt) {
            $this->response([
                'status' => false,
                'message' => '收款单不存在'
            ], 404);
            return;
        }

        // 检查状态转换是否有效
        $valid_transitions = [
            'draft' => ['confirmed', 'cancelled'],
            'confirmed' => ['cancelled'],
            'cancelled' => []
        ];

        if (!in_array($status, $valid_transitions[$receipt['status']])) {
            $this->response([
                'status' => false,
                'message' => '无效的状态转换：从 ' . $receipt['status'] . ' 到 ' . $status
            ], 400);
            return;
        }

        // 更新收款单状态
        $result = $this->finance_model->update_receipt_status($id, $status, $this->user_id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新收款单状态失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新收款单状态成功'
        ], 200);
    }

    /**
     * 删除收款单
     * DELETE /api/finance/receipts/:id
     */
    public function receipt_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '收款单ID不能为空'
            ], 400);
            return;
        }

        // 检查收款单是否存在
        $receipt = $this->finance_model->get_receipt_by_id($id);
        if (!$receipt) {
            $this->response([
                'status' => false,
                'message' => '收款单不存在'
            ], 404);
            return;
        }

        // 只能删除草稿状态的收款单
        if ($receipt['status'] != 'draft') {
            $this->response([
                'status' => false,
                'message' => '只能删除草稿状态的收款单'
            ], 400);
            return;
        }

        // 删除收款单
        $result = $this->finance_model->delete_receipt($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除收款单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除收款单成功'
        ], 200);
    }

    /**
     * 获取付款单列表
     * GET /api/finance/payments
     */
    public function payments_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'supplier_id' => $this->get('supplier_id', true),
            'status' => $this->get('status', true),
            'start_date' => $this->get('start_date', true),
            'end_date' => $this->get('end_date', true)
        ];

        // 获取付款单列表和总数
        $payments = $this->finance_model->get_payments($params);
        $total = $this->finance_model->count_payments($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $payments,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取付款单详情
     * GET /api/finance/payments/:id
     */
    public function payment_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '付款单ID不能为空'
            ], 400);
            return;
        }

        // 获取付款单详情
        $payment = $this->finance_model->get_payment_by_id($id);
        if (!$payment) {
            $this->response([
                'status' => false,
                'message' => '付款单不存在'
            ], 404);
            return;
        }

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $payment
        ], 200);
    }

    /**
     * 创建付款单
     * POST /api/finance/payments
     */
    public function payment_post() {
        // 获取请求数据
        $payment_data = [
            'payment_no' => $this->post('payment_no', true),
            'supplier_id' => $this->post('supplier_id', true),
            'payment_date' => $this->post('payment_date', true),
            'amount' => $this->post('amount', true),
            'payment_method' => $this->post('payment_method', true),
            'reference_no' => $this->post('reference_no', true),
            'order_id' => $this->post('order_id', true),
            'status' => 'draft',
            'remark' => $this->post('remark', true),
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$payment_data['supplier_id']) {
            $this->response([
                'status' => false,
                'message' => '供应商不能为空'
            ], 400);
            return;
        }

        if (!$payment_data['payment_date']) {
            $this->response([
                'status' => false,
                'message' => '付款日期不能为空'
            ], 400);
            return;
        }

        if (!$payment_data['amount'] || $payment_data['amount'] <= 0) {
            $this->response([
                'status' => false,
                'message' => '付款金额必须大于0'
            ], 400);
            return;
        }

        if (!$payment_data['payment_method']) {
            $this->response([
                'status' => false,
                'message' => '支付方式不能为空'
            ], 400);
            return;
        }

        // 验证供应商是否存在
        $supplier = $this->supplier_model->get_supplier_by_id($payment_data['supplier_id']);
        if (!$supplier) {
            $this->response([
                'status' => false,
                'message' => '供应商不存在'
            ], 400);
            return;
        }

        // 如果关联了采购订单，验证订单是否存在
        if ($payment_data['order_id']) {
            $order = $this->purchase_model->get_order_by_id($payment_data['order_id']);
            if (!$order) {
                $this->response([
                    'status' => false,
                    'message' => '采购订单不存在'
                ], 400);
                return;
            }

            // 验证订单供应商是否匹配
            if ($order['supplier_id'] != $payment_data['supplier_id']) {
                $this->response([
                    'status' => false,
                    'message' => '付款单供应商与采购订单供应商不匹配'
                ], 400);
                return;
            }
        }

        // 如果没有提供付款单号，自动生成
        if (empty($payment_data['payment_no'])) {
            $payment_data['payment_no'] = 'PM' . date('YmdHis');
        }

        // 创建付款单
        $payment_id = $this->finance_model->create_payment($payment_data);
        if (!$payment_id) {
            $this->response([
                'status' => false,
                'message' => '创建付款单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建付款单成功',
            'data' => [
                'id' => $payment_id,
                'payment_no' => $payment_data['payment_no']
            ]
        ], 201);
    }

    /**
     * 更新付款单状态
     * PUT /api/finance/payments/:id/status
     */
    public function payment_status_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '付款单ID不能为空'
            ], 400);
            return;
        }

        // 获取请求数据
        $status = $this->put('status', true);
        if (!$status) {
            $this->response([
                'status' => false,
                'message' => '状态不能为空'
            ], 400);
            return;
        }

        // 检查付款单是否存在
        $payment = $this->finance_model->get_payment_by_id($id);
        if (!$payment) {
            $this->response([
                'status' => false,
                'message' => '付款单不存在'
            ], 404);
            return;
        }

        // 检查状态转换是否有效
        $valid_transitions = [
            'draft' => ['confirmed', 'cancelled'],
            'confirmed' => ['cancelled'],
            'cancelled' => []
        ];

        if (!in_array($status, $valid_transitions[$payment['status']])) {
            $this->response([
                'status' => false,
                'message' => '无效的状态转换：从 ' . $payment['status'] . ' 到 ' . $status
            ], 400);
            return;
        }

        // 更新付款单状态
        $result = $this->finance_model->update_payment_status($id, $status, $this->user_id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新付款单状态失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新付款单状态成功'
        ], 200);
    }

    /**
     * 删除付款单
     * DELETE /api/finance/payments/:id
     */
    public function payment_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '付款单ID不能为空'
            ], 400);
            return;
        }

        // 检查付款单是否存在
        $payment = $this->finance_model->get_payment_by_id($id);
        if (!$payment) {
            $this->response([
                'status' => false,
                'message' => '付款单不存在'
            ], 404);
            return;
        }

        // 只能删除草稿状态的付款单
        if ($payment['status'] != 'draft') {
            $this->response([
                'status' => false,
                'message' => '只能删除草稿状态的付款单'
            ], 400);
            return;
        }

        // 删除付款单
        $result = $this->finance_model->delete_payment($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除付款单失败'
            ], 500);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除付款单成功'
        ], 200);
    }

    /**
     * 获取客户应收账款报表
     * GET /api/finance/receivables
     */
    public function receivables_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'customer_id' => $this->get('customer_id', true),
            'overdue' => $this->get('overdue', true),
            'as_of_date' => $this->get('as_of_date', true) ? $this->get('as_of_date', true) : date('Y-m-d')
        ];

        // 获取应收账款报表
        $receivables = $this->finance_model->get_receivables($params);
        $total = $this->finance_model->count_receivables($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $receivables,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取供应商应付账款报表
     * GET /api/finance/payables
     */
    public function payables_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? intval($this->get('page', true)) : 1,
            'limit' => $this->get('limit', true) ? intval($this->get('limit', true)) : 10,
            'keyword' => $this->get('keyword', true),
            'supplier_id' => $this->get('supplier_id', true),
            'overdue' => $this->get('overdue', true),
            'as_of_date' => $this->get('as_of_date', true) ? $this->get('as_of_date', true) : date('Y-m-d')
        ];

        // 获取应付账款报表
        $payables = $this->finance_model->get_payables($params);
        $total = $this->finance_model->count_payables($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $payables,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取收支汇总报表
     * GET /api/finance/income-expense-summary
     */
    public function income_expense_summary_get() {
        // 获取查询参数
        $start_date = $this->get('start_date', true) ? $this->get('start_date', true) : date('Y-m-01');
        $end_date = $this->get('end_date', true) ? $this->get('end_date', true) : date('Y-m-d');
        $group_by = $this->get('group_by', true) ? $this->get('group_by', true) : 'day';

        // 获取收支汇总报表
        $summary = $this->finance_model->get_income_expense_summary($start_date, $end_date, $group_by);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => $summary
        ], 200);
    }
}
