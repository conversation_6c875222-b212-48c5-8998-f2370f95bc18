<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 客户模型类
 * 处理客户相关的数据库操作
 */
class Customer_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取客户列表
     * @param array $params 查询参数
     * @return array 客户列表
     */
    public function get_customers($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('c.*, cat.name as category_name');
        $this->db->from('customers c');
        $this->db->join('categories cat', 'c.category_id = cat.id', 'left');
        $this->db->where('c.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('c.name', $keyword);
            $this->db->or_like('c.code', $keyword);
            $this->db->or_like('c.contact_name', $keyword);
            $this->db->or_like('c.contact_phone', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('c.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('c.status', $params['status']);
        }
        
        $this->db->order_by('c.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取客户总数
     * @param array $params 查询参数
     * @return int 客户总数
     */
    public function count_customers($params = []) {
        $this->db->from('customers c');
        $this->db->where('c.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('c.name', $keyword);
            $this->db->or_like('c.code', $keyword);
            $this->db->or_like('c.contact_name', $keyword);
            $this->db->or_like('c.contact_phone', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('c.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('c.status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取客户信息
     * @param int $id 客户ID
     * @return array|null 客户信息
     */
    public function get_customer_by_id($id) {
        $this->db->select('c.*, cat.name as category_name');
        $this->db->from('customers c');
        $this->db->join('categories cat', 'c.category_id = cat.id', 'left');
        $this->db->where('c.id', $id);
        $this->db->where('c.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据编码获取客户信息
     * @param string $code 客户编码
     * @return array|null 客户信息
     */
    public function get_customer_by_code($code) {
        $this->db->from('customers');
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 检查客户编码是否存在
     * @param string $code 客户编码
     * @param int $exclude_id 排除的客户ID
     * @return bool 是否存在
     */
    public function is_code_exists($code, $exclude_id = 0) {
        $this->db->from('customers');
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id > 0) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 创建客户
     * @param array $data 客户数据
     * @return int|bool 客户ID或false
     */
    public function create_customer($data) {
        $this->db->insert('customers', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新客户
     * @param int $id 客户ID
     * @param array $data 客户数据
     * @return bool 是否成功
     */
    public function update_customer($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('customers', $data);
    }
    
    /**
     * 删除客户（软删除）
     * @param int $id 客户ID
     * @return bool 是否成功
     */
    public function delete_customer($id) {
        $this->db->where('id', $id);
        return $this->db->update('customers', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 检查客户是否被引用
     * @param int $id 客户ID
     * @return bool 是否被引用
     */
    public function check_customer_in_use($id) {
        // 检查销售订单
        $this->db->from('sales_orders');
        $this->db->where('customer_id', $id);
        $this->db->where('is_deleted', 0);
        if ($this->db->count_all_results() > 0) {
            return true;
        }
        
        // 检查销售出库单
        $this->db->from('sales_deliveries');
        $this->db->where('customer_id', $id);
        $this->db->where('is_deleted', 0);
        if ($this->db->count_all_results() > 0) {
            return true;
        }
        
        // 检查收款记录
        $this->db->from('receipts');
        $this->db->where('customer_id', $id);
        $this->db->where('is_deleted', 0);
        if ($this->db->count_all_results() > 0) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 获取客户应收账款
     * @param int $id 客户ID
     * @return array 应收账款信息
     */
    public function get_customer_receivables($id) {
        $this->db->select('SUM(grand_total) as total_amount');
        $this->db->from('sales_orders');
        $this->db->where('customer_id', $id);
        $this->db->where('status', 'completed');
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        $total_sales = $query->row_array()['total_amount'] ?: 0;
        
        $this->db->select('SUM(amount) as total_amount');
        $this->db->from('receipts');
        $this->db->where('customer_id', $id);
        $this->db->where('status', 'confirmed');
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        $total_receipts = $query->row_array()['total_amount'] ?: 0;
        
        return [
            'total_sales' => $total_sales,
            'total_receipts' => $total_receipts,
            'balance' => $total_sales - $total_receipts
        ];
    }
}
