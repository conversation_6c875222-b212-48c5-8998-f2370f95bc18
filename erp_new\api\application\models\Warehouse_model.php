<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 仓库管理模型类
 * 处理仓库相关的数据库操作
 */
class Warehouse_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取仓库列表
     * @param array $params 查询参数
     * @return array 仓库列表
     */
    public function get_warehouses($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->from('warehouses');
        $this->db->where('is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('name', $keyword);
            $this->db->or_like('code', $keyword);
            $this->db->or_like('address', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['status']) && $params['status'] !== '') {
            $this->db->where('status', $params['status']);
        }
        
        // 是否需要分页
        if (isset($params['no_paging']) && $params['no_paging']) {
            $this->db->order_by('sort_order', 'ASC');
            $this->db->order_by('id', 'ASC');
            $query = $this->db->get();
            return $query->result_array();
        } else {
            $this->db->order_by('sort_order', 'ASC');
            $this->db->order_by('id', 'ASC');
            $this->db->limit($limit, $offset);
            $query = $this->db->get();
            return $query->result_array();
        }
    }
    
    /**
     * 获取仓库总数
     * @param array $params 查询参数
     * @return int 仓库总数
     */
    public function count_warehouses($params = []) {
        $this->db->from('warehouses');
        $this->db->where('is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('name', $keyword);
            $this->db->or_like('code', $keyword);
            $this->db->or_like('address', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['status']) && $params['status'] !== '') {
            $this->db->where('status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取仓库信息
     * @param int $id 仓库ID
     * @return array|null 仓库信息
     */
    public function get_warehouse_by_id($id) {
        $this->db->from('warehouses');
        $this->db->where('id', $id);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据编码获取仓库信息
     * @param string $code 仓库编码
     * @return array|null 仓库信息
     */
    public function get_warehouse_by_code($code) {
        $this->db->from('warehouses');
        $this->db->where('code', $code);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据名称获取仓库信息
     * @param string $name 仓库名称
     * @return array|null 仓库信息
     */
    public function get_warehouse_by_name($name) {
        $this->db->from('warehouses');
        $this->db->where('name', $name);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 创建仓库
     * @param array $data 仓库数据
     * @return int|bool 成功返回仓库ID，失败返回false
     */
    public function create_warehouse($data) {
        $this->db->insert('warehouses', $data);
        return $this->db->insert_id() ? $this->db->insert_id() : false;
    }
    
    /**
     * 更新仓库
     * @param int $id 仓库ID
     * @param array $data 仓库数据
     * @return bool 成功返回true，失败返回false
     */
    public function update_warehouse($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('warehouses', $data);
    }
    
    /**
     * 删除仓库（软删除）
     * @param int $id 仓库ID
     * @return bool 成功返回true，失败返回false
     */
    public function delete_warehouse($id) {
        $data = [
            'is_deleted' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        $this->db->where('id', $id);
        return $this->db->update('warehouses', $data);
    }
    
    /**
     * 批量删除仓库（软删除）
     * @param array $ids 仓库ID数组
     * @return bool 成功返回true，失败返回false
     */
    public function batch_delete_warehouses($ids) {
        $data = [
            'is_deleted' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        $this->db->where_in('id', $ids);
        return $this->db->update('warehouses', $data);
    }
    
    /**
     * 批量更新仓库
     * @param array $data 仓库数据数组，每个元素必须包含id
     * @return bool 成功返回true，失败返回false
     */
    public function batch_update_warehouses($data) {
        $this->db->trans_start();
        
        foreach ($data as $item) {
            $id = $item['id'];
            unset($item['id']);
            
            $this->db->where('id', $id);
            $this->db->update('warehouses', $item);
        }
        
        $this->db->trans_complete();
        return $this->db->trans_status();
    }
    
    /**
     * 检查仓库是否在使用
     * @param int $id 仓库ID
     * @return bool 使用中返回true，否则返回false
     */
    public function is_warehouse_in_use($id) {
        // 检查库存记录
        $this->db->from('inventory');
        $this->db->where('warehouse_id', $id);
        $this->db->where('quantity >', 0);
        $inventory_count = $this->db->count_all_results();
        
        if ($inventory_count > 0) {
            return true;
        }
        
        // 检查订单记录（假设有订单表与仓库关联）
        $this->db->from('sales_orders');
        $this->db->where('warehouse_id', $id);
        $this->db->where('is_deleted', 0);
        $sales_count = $this->db->count_all_results();
        
        if ($sales_count > 0) {
            return true;
        }
        
        $this->db->from('purchase_orders');
        $this->db->where('warehouse_id', $id);
        $this->db->where('is_deleted', 0);
        $purchase_count = $this->db->count_all_results();
        
        if ($purchase_count > 0) {
            return true;
        }
        
        return false;
    }
}
