<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 销售管理控制器
 * 实现销售订单和出库等功能
 */
class Sales extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('sales_model');
        $this->load->model('customer_model');
        $this->load->model('product_model');
        $this->load->model('inventory_model');
    }

    /**
     * 获取销售订单列表
     * GET /api/sales/orders
     */
    public function orders_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? $this->get('page', true) : 1,
            'limit' => $this->get('limit', true) ? $this->get('limit', true) : 10,
            'keyword' => $this->get('keyword', true),
            'customer_id' => $this->get('customer_id', true),
            'status' => $this->get('status', true),
            'start_date' => $this->get('start_date', true),
            'end_date' => $this->get('end_date', true)
        ];

        // 获取销售订单列表和总数
        $orders = $this->sales_model->get_orders($params);
        $total = $this->sales_model->count_orders($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $orders,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取销售订单详情
     * GET /api/sales/orders/:id
     */
    public function order_detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '订单ID不能为空'
            ], 400);
            return;
        }

        $order = $this->sales_model->get_order_by_id($id);
        if (!$order) {
            $this->response([
                'status' => false,
                'message' => '订单不存在'
            ], 404);
            return;
        }

        $this->response([
            'status' => true,
            'data' => $order
        ], 200);
    }

    /**
     * 创建销售订单
     * POST /api/sales/orders
     */
    public function create_order_post() {
        // 获取请求数据
        $data = [
            'order_no' => $this->post('order_no', true),
            'customer_id' => $this->post('customer_id', true),
            'order_date' => $this->post('order_date', true),
            'delivery_date' => $this->post('delivery_date', true),
            'total_amount' => $this->post('total_amount', true),
            'discount_amount' => $this->post('discount_amount', true),
            'tax_amount' => $this->post('tax_amount', true),
            'grand_total' => $this->post('grand_total', true),
            'status' => 'draft',
            'remark' => $this->post('remark', true),
            'created_by' => $this->post('created_by', true) ? $this->post('created_by', true) : 1,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 获取订单详情
        $details = $this->post('details', true);
        if (!is_array($details) || count($details) == 0) {
            $this->response([
                'status' => false,
                'message' => '订单详情不能为空'
            ], 400);
            return;
        }

        // 验证必填字段
        if (!$data['customer_id'] || !$data['order_date']) {
            $this->response([
                'status' => false,
                'message' => '客户和订单日期不能为空'
            ], 400);
            return;
        }

        // 生成订单号
        if (!$data['order_no']) {
            $data['order_no'] = 'SO' . date('YmdHis');
        }

        // 创建订单
        $order_id = $this->sales_model->create_order($data, $details);
        if (!$order_id) {
            $this->response([
                'status' => false,
                'message' => '创建订单失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '创建订单成功',
            'data' => [
                'id' => $order_id,
                'order_no' => $data['order_no']
            ]
        ], 201);
    }

    /**
     * 更新销售订单
     * PUT /api/sales/orders/:id
     */
    public function update_order_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '订单ID不能为空'
            ], 400);
            return;
        }

        // 检查订单是否存在
        $order = $this->sales_model->get_order_by_id($id);
        if (!$order) {
            $this->response([
                'status' => false,
                'message' => '订单不存在'
            ], 404);
            return;
        }

        // 检查订单状态
        if ($order['status'] != 'draft') {
            $this->response([
                'status' => false,
                'message' => '只能修改草稿状态的订单'
            ], 400);
            return;
        }

        // 获取请求数据
        $data = [
            'customer_id' => $this->put('customer_id', true),
            'order_date' => $this->put('order_date', true),
            'delivery_date' => $this->put('delivery_date', true),
            'total_amount' => $this->put('total_amount', true),
            'discount_amount' => $this->put('discount_amount', true),
            'tax_amount' => $this->put('tax_amount', true),
            'grand_total' => $this->put('grand_total', true),
            'remark' => $this->put('remark', true),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 获取订单详情
        $details = $this->put('details', true);

        // 更新订单
        $result = $this->sales_model->update_order($id, $data, $details);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新订单失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '更新订单成功'
        ], 200);
    }
}
