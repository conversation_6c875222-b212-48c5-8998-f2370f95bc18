<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
*/

$route['default_controller'] = 'test';
$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;

// API路由配置
// 用户认证API
$route['auth/login']['post'] = 'auth/login';
$route['auth/logout']['post'] = 'auth/logout';
$route['auth/refresh_token']['post'] = 'auth/refresh_token';
$route['auth/user_info']['get'] = 'auth/user_info';
$route['auth/verify_token']['post'] = 'auth/verify_token';
$route['auth/change_password']['post'] = 'auth/change_password';

// 用户管理API
$route['api/users'] = 'users/index';
$route['api/users/(:num)'] = 'users/show/$1';
$route['api/users/create'] = 'users/create';
$route['api/users/update/(:num)'] = 'users/update/$1';
$route['api/users/delete/(:num)'] = 'users/delete/$1';

// 产品管理API
$route['api/products'] = 'product/index_get';
$route['api/products/(:num)'] = 'product/detail_get/$1';
$route['api/products/batch-update'] = 'product/batch_update_post';
$route['api/products/batch-delete'] = 'product/batch_delete_post';
$route['api/product-categories'] = 'product/categories_get';

// 分类管理API
$route['api/categories']['get'] = 'category/index_get';
$route['api/categories']['post'] = 'category/create_post';
$route['api/categories/(:num)']['get'] = 'category/detail_get/$1';
$route['api/categories/(:num)']['put'] = 'category/update_put/$1';
$route['api/categories/(:num)']['delete'] = 'category/delete_delete/$1';

// RESTful API 路由
$route['api/products']['get'] = 'product/index_get';
$route['api/products']['post'] = 'product/create_post';
$route['api/products/(:num)']['get'] = 'product/detail_get/$1';
$route['api/products/(:num)']['put'] = 'product/update_put/$1';
$route['api/products/(:num)']['delete'] = 'product/delete_delete/$1';

// 客户管理API
$route['api/customers'] = 'customers/index';
$route['api/customers/(:num)'] = 'customers/show/$1';
$route['api/customers/create'] = 'customers/create';
$route['api/customers/update/(:num)'] = 'customers/update/$1';
$route['api/customers/delete/(:num)'] = 'customers/delete/$1';

// 供应商管理API
$route['api/suppliers'] = 'suppliers/index';
$route['api/suppliers/(:num)'] = 'suppliers/show/$1';
$route['api/suppliers/create'] = 'suppliers/create';
$route['api/suppliers/update/(:num)'] = 'suppliers/update/$1';
$route['api/suppliers/delete/(:num)'] = 'suppliers/delete/$1';

// 销售管理API
$route['api/sales'] = 'sales/index';
$route['api/sales/(:num)'] = 'sales/show/$1';
$route['api/sales/create'] = 'sales/create';

// 测试API
$route['test'] = 'test/index';

// 库存管理API
$route['inventory/items'] = 'inventory/items';
$route['inventory/warehouses'] = 'inventory/warehouses';

// 库存调拨API
$route['inventory/transfers'] = 'inventory/transfers';
$route['inventory/transfers/(:num)'] = 'inventory/transfer_detail/$1';
$route['inventory/transfers/(:num)/submit'] = 'inventory/transfer_submit/$1';
$route['inventory/transfers/(:num)/approve'] = 'inventory/transfer_approve/$1';
$route['inventory/transfers/(:num)/complete'] = 'inventory/transfer_complete/$1';
$route['inventory/transfers/(:num)/cancel'] = 'inventory/transfer_cancel/$1';
$route['api/sales/update/(:num)'] = 'sales/update/$1';
$route['api/sales/delete/(:num)'] = 'sales/delete/$1';

// 采购管理API
$route['api/purchases'] = 'purchases/index';
$route['api/purchases/(:num)'] = 'purchases/show/$1';
$route['api/purchases/create'] = 'purchases/create';
$route['api/purchases/update/(:num)'] = 'purchases/update/$1';
$route['api/purchases/delete/(:num)'] = 'purchases/delete/$1';

// 库存管理API
$route['api/inventory'] = 'inventory/index';
$route['api/inventory/(:num)'] = 'inventory/show/$1';
$route['api/inventory/stock'] = 'inventory/stock';
$route['api/inventory/transfer'] = 'inventory/transfer';
$route['api/inventory/count'] = 'inventory/count';

// 仓库管理API
$route['api/warehouses']['get'] = 'warehouse/index_get';
$route['api/warehouses']['post'] = 'warehouse/create_post';
$route['api/warehouses/(:num)']['get'] = 'warehouse/detail_get/$1';
$route['api/warehouses/(:num)']['put'] = 'warehouse/update_put/$1';
$route['api/warehouses/(:num)']['delete'] = 'warehouse/delete_delete/$1';
$route['api/warehouses/batch-update']['post'] = 'warehouse/batch_update_post';
$route['api/warehouses/batch-delete']['post'] = 'warehouse/batch_delete_post';

// 财务管理API
$route['api/finance/receipts'] = 'finance/receipts';
$route['api/finance/payments'] = 'finance/payments';
$route['api/finance/receipts/create'] = 'finance/create_receipt';
$route['api/finance/payments/create'] = 'finance/create_payment';

// 报表API
$route['api/reports/sales'] = 'reports/sales';
$route['api/reports/purchases'] = 'reports/purchases';
$route['api/reports/inventory'] = 'reports/inventory';
$route['api/reports/finance'] = 'reports/finance';

// 用户管理API（新增）
$route['api/user/login']['post'] = 'user/login_post';
$route['api/user/profile']['get'] = 'user/profile_get';
$route['api/user/password']['put'] = 'user/password_put';
$route['api/user/users']['get'] = 'user/users_get';
$route['api/user/users/(:num)']['get'] = 'user/user_get/$1';
$route['api/user/users']['post'] = 'user/user_post';
$route['api/user/users/(:num)']['put'] = 'user/user_put/$1';
$route['api/user/users/(:num)']['delete'] = 'user/user_delete/$1';

// 角色管理API（新增）
$route['api/role/roles']['get'] = 'role/roles_get';
$route['api/role/roles/(:num)']['get'] = 'role/role_get/$1';
$route['api/role/roles']['post'] = 'role/role_post';
$route['api/role/roles/(:num)']['put'] = 'role/role_put/$1';
$route['api/role/roles/(:num)']['delete'] = 'role/role_delete/$1';
$route['api/role/permissions']['get'] = 'role/permissions_get';
$route['api/role/permissions/(:num)']['put'] = 'role/permissions_put/$1';

// 系统设置API（新增）
$route['api/setting/all']['get'] = 'setting/all_get';
$route['api/setting/company']['get'] = 'setting/company_get';
$route['api/setting/company']['put'] = 'setting/company_put';
$route['api/setting/system']['get'] = 'setting/system_get';
$route['api/setting/system']['put'] = 'setting/system_put';
$route['api/setting/upload-logo']['post'] = 'setting/upload_logo_post';
$route['api/setting/dictionaries']['get'] = 'setting/dictionaries_get';
$route['api/setting/dictionaries/(:any)']['get'] = 'setting/dictionary_type_get/$1';
$route['api/setting/dictionaries']['post'] = 'setting/dictionary_post';
$route['api/setting/dictionaries/(:num)']['put'] = 'setting/dictionary_put/$1';
$route['api/setting/dictionaries/(:num)']['delete'] = 'setting/dictionary_delete/$1';

// 数据导入导出API（新增）
$route['api/export/products']['get'] = 'import_export/export_products_get';
$route['api/import/products']['post'] = 'import_export/import_products_post';
$route['api/export/customers']['get'] = 'import_export/export_customers_get';
$route['api/import/customers']['post'] = 'import_export/import_customers_post';
$route['api/export/suppliers']['get'] = 'import_export/export_suppliers_get';
$route['api/import/suppliers']['post'] = 'import_export/import_suppliers_post';

// 打印功能API（新增）
$route['api/print/templates']['get'] = 'print/templates_get';
$route['api/print/template/(:num)']['get'] = 'print/template_get/$1';
$route['api/print/template']['post'] = 'print/template_post';
$route['api/print/template/(:num)']['put'] = 'print/template_put/$1';
$route['api/print/template/(:num)']['delete'] = 'print/template_delete/$1';
$route['api/print/template/(:num)/default']['put'] = 'print/default_put/$1';
$route['api/print/sale/(:num)']['get'] = 'print/sale_get/$1';
$route['api/print/purchase/(:num)']['get'] = 'print/purchase_get/$1';
$route['api/print/inventory/(:num)']['get'] = 'print/inventory_get/$1';
