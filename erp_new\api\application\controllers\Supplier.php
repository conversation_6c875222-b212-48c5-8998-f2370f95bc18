<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 供应商管理控制器
 * 实现供应商相关的API接口
 */
class Supplier extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('supplier_model');
        $this->load->model('category_model');
    }

    /**
     * 获取供应商列表
     * GET /api/suppliers
     */
    public function index_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? $this->get('page', true) : 1,
            'limit' => $this->get('limit', true) ? $this->get('limit', true) : 10,
            'keyword' => $this->get('keyword', true),
            'category_id' => $this->get('category_id', true),
            'status' => $this->get('status', true)
        ];

        // 获取供应商列表和总数
        $suppliers = $this->supplier_model->get_suppliers($params);
        $total = $this->supplier_model->count_suppliers($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $suppliers,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取单个供应商详情
     * GET /api/suppliers/:id
     */
    public function detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '供应商ID不能为空'
            ], 400);
            return;
        }

        $supplier = $this->supplier_model->get_supplier_by_id($id);
        if (!$supplier) {
            $this->response([
                'status' => false,
                'message' => '供应商不存在'
            ], 404);
            return;
        }

        $this->response([
            'status' => true,
            'data' => $supplier
        ], 200);
    }

    /**
     * 创建供应商
     * POST /api/suppliers
     */
    public function create_post() {
        // 获取请求数据
        $data = [
            'name' => $this->post('name', true),
            'code' => $this->post('code', true),
            'category_id' => $this->post('category_id', true),
            'contact_name' => $this->post('contact_name', true),
            'contact_phone' => $this->post('contact_phone', true),
            'contact_email' => $this->post('contact_email', true),
            'address' => $this->post('address', true),
            'tax_number' => $this->post('tax_number', true),
            'bank_name' => $this->post('bank_name', true),
            'bank_account' => $this->post('bank_account', true),
            'payment_term' => $this->post('payment_term', true),
            'status' => $this->post('status', true) ? $this->post('status', true) : 'active',
            'remark' => $this->post('remark', true),
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$data['name']) {
            $this->response([
                'status' => false,
                'message' => '供应商名称不能为空'
            ], 400);
            return;
        }

        // 验证供应商编码唯一性
        if ($data['code'] && $this->supplier_model->is_code_exists($data['code'])) {
            $this->response([
                'status' => false,
                'message' => '供应商编码已存在'
            ], 400);
            return;
        }

        // 如果没有提供编码，则自动生成
        if (!$data['code']) {
            $data['code'] = 'S' . date('YmdHis');
        }

        // 创建供应商
        $supplier_id = $this->supplier_model->create_supplier($data);
        if (!$supplier_id) {
            $this->response([
                'status' => false,
                'message' => '创建供应商失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '创建供应商成功',
            'data' => [
                'id' => $supplier_id,
                'code' => $data['code']
            ]
        ], 201);
    }

    /**
     * 更新供应商
     * PUT /api/suppliers/:id
     */
    public function update_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '供应商ID不能为空'
            ], 400);
            return;
        }

        // 检查供应商是否存在
        $supplier = $this->supplier_model->get_supplier_by_id($id);
        if (!$supplier) {
            $this->response([
                'status' => false,
                'message' => '供应商不存在'
            ], 404);
            return;
        }

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'code' => $this->put('code', true),
            'category_id' => $this->put('category_id', true),
            'contact_name' => $this->put('contact_name', true),
            'contact_phone' => $this->put('contact_phone', true),
            'contact_email' => $this->put('contact_email', true),
            'address' => $this->put('address', true),
            'tax_number' => $this->put('tax_number', true),
            'bank_name' => $this->put('bank_name', true),
            'bank_account' => $this->put('bank_account', true),
            'payment_term' => $this->put('payment_term', true),
            'status' => $this->put('status', true),
            'remark' => $this->put('remark', true),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (isset($data['name']) && !$data['name']) {
            $this->response([
                'status' => false,
                'message' => '供应商名称不能为空'
            ], 400);
            return;
        }

        // 验证供应商编码唯一性
        if (isset($data['code']) && $data['code'] && $data['code'] !== $supplier['code'] && $this->supplier_model->is_code_exists($data['code'])) {
            $this->response([
                'status' => false,
                'message' => '供应商编码已存在'
            ], 400);
            return;
        }

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 更新供应商
        $result = $this->supplier_model->update_supplier($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新供应商失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '更新供应商成功'
        ], 200);
    }

    /**
     * 删除供应商
     * DELETE /api/suppliers/:id
     */
    public function delete_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '供应商ID不能为空'
            ], 400);
            return;
        }

        // 检查供应商是否存在
        $supplier = $this->supplier_model->get_supplier_by_id($id);
        if (!$supplier) {
            $this->response([
                'status' => false,
                'message' => '供应商不存在'
            ], 404);
            return;
        }

        // 检查供应商是否被引用
        if ($this->supplier_model->check_supplier_in_use($id)) {
            $this->response([
                'status' => false,
                'message' => '该供应商已被订单或其他记录引用，无法删除'
            ], 400);
            return;
        }

        // 删除供应商（软删除）
        $result = $this->supplier_model->delete_supplier($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除供应商失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '删除供应商成功'
        ], 200);
    }

    /**
     * 获取供应商分类列表
     * GET /api/supplier-categories
     */
    public function categories_get() {
        $categories = $this->category_model->get_categories('supplier');
        
        $this->response([
            'status' => true,
            'data' => $categories
        ], 200);
    }
}
