<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 数据导入导出控制器
 * 实现Excel导入导出相关的API接口
 */
class Import_export extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('import_export_model');
        // 加载其他需要的模型
        $this->load->model('product_model');
        $this->load->model('customer_model');
        $this->load->model('supplier_model');
        $this->load->model('inventory_model');
        
        // 加载PHPExcel库
        require_once APPPATH . 'third_party/PHPExcel/PHPExcel.php';
    }

    /**
     * 导出产品数据
     * GET /api/export/products
     */
    public function export_products_get() {
        // 检查权限
        if (!$this->has_permission('product_export')) {
            $this->response([
                'status' => false,
                'message' => '无权限导出产品数据'
            ], 403);
            return;
        }

        // 获取查询参数
        $params = [
            'category_id' => $this->get('category_id', true),
            'status' => $this->get('status', true),
            'keyword' => $this->get('keyword', true)
        ];

        // 获取产品数据
        $products = $this->product_model->get_products_for_export($params);

        // 导出数据到Excel
        $excel_file = $this->import_export_model->export_products($products);
        if (!$excel_file) {
            $this->response([
                'status' => false,
                'message' => '导出产品数据失败'
            ], 500);
            return;
        }

        // 返回Excel文件URL
        $this->response([
            'status' => true,
            'message' => '导出产品数据成功',
            'data' => [
                'file_url' => base_url($excel_file)
            ]
        ], 200);
    }

    /**
     * 导入产品数据
     * POST /api/import/products
     */
    public function import_products_post() {
        // 检查权限
        if (!$this->has_permission('product_import')) {
            $this->response([
                'status' => false,
                'message' => '无权限导入产品数据'
            ], 403);
            return;
        }

        // 检查上传目录
        $upload_dir = './uploads/import/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // 配置上传参数
        $config['upload_path'] = $upload_dir;
        $config['allowed_types'] = 'xlsx|xls';
        $config['max_size'] = 5120; // 5MB
        $config['file_name'] = 'products_import_' . time();

        $this->load->library('upload', $config);

        // 执行上传
        if (!$this->upload->do_upload('file')) {
            $error = $this->upload->display_errors('', '');
            $this->response([
                'status' => false,
                'message' => $error
            ], 400);
            return;
        }

        // 获取上传文件信息
        $upload_data = $this->upload->data();
        $file_path = $upload_data['full_path'];

        // 导入Excel文件数据
        $result = $this->import_export_model->import_products($file_path);
        if (!$result['success']) {
            $this->response([
                'status' => false,
                'message' => $result['message']
            ], 400);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '导入产品数据成功',
            'data' => [
                'imported' => $result['imported'],
                'failed' => $result['failed'],
                'errors' => $result['errors']
            ]
        ], 200);
    }

    /**
     * 导出客户数据
     * GET /api/export/customers
     */
    public function export_customers_get() {
        // 检查权限
        if (!$this->has_permission('customer_export')) {
            $this->response([
                'status' => false,
                'message' => '无权限导出客户数据'
            ], 403);
            return;
        }

        // 获取查询参数
        $params = [
            'category_id' => $this->get('category_id', true),
            'status' => $this->get('status', true),
            'keyword' => $this->get('keyword', true)
        ];

        // 获取客户数据
        $customers = $this->customer_model->get_customers_for_export($params);

        // 导出数据到Excel
        $excel_file = $this->import_export_model->export_customers($customers);
        if (!$excel_file) {
            $this->response([
                'status' => false,
                'message' => '导出客户数据失败'
            ], 500);
            return;
        }

        // 返回Excel文件URL
        $this->response([
            'status' => true,
            'message' => '导出客户数据成功',
            'data' => [
                'file_url' => base_url($excel_file)
            ]
        ], 200);
    }

    /**
     * 导入客户数据
     * POST /api/import/customers
     */
    public function import_customers_post() {
        // 检查权限
        if (!$this->has_permission('customer_import')) {
            $this->response([
                'status' => false,
                'message' => '无权限导入客户数据'
            ], 403);
            return;
        }

        // 检查上传目录
        $upload_dir = './uploads/import/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // 配置上传参数
        $config['upload_path'] = $upload_dir;
        $config['allowed_types'] = 'xlsx|xls';
        $config['max_size'] = 5120; // 5MB
        $config['file_name'] = 'customers_import_' . time();

        $this->load->library('upload', $config);

        // 执行上传
        if (!$this->upload->do_upload('file')) {
            $error = $this->upload->display_errors('', '');
            $this->response([
                'status' => false,
                'message' => $error
            ], 400);
            return;
        }

        // 获取上传文件信息
        $upload_data = $this->upload->data();
        $file_path = $upload_data['full_path'];

        // 导入Excel文件数据
        $result = $this->import_export_model->import_customers($file_path);
        if (!$result['success']) {
            $this->response([
                'status' => false,
                'message' => $result['message']
            ], 400);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '导入客户数据成功',
            'data' => [
                'imported' => $result['imported'],
                'failed' => $result['failed'],
                'errors' => $result['errors']
            ]
        ], 200);
    }

    /**
     * 导出供应商数据
     * GET /api/export/suppliers
     */
    public function export_suppliers_get() {
        // 检查权限
        if (!$this->has_permission('supplier_export')) {
            $this->response([
                'status' => false,
                'message' => '无权限导出供应商数据'
            ], 403);
            return;
        }

        // 获取查询参数
        $params = [
            'category_id' => $this->get('category_id', true),
            'status' => $this->get('status', true),
            'keyword' => $this->get('keyword', true)
        ];

        // 获取供应商数据
        $suppliers = $this->supplier_model->get_suppliers_for_export($params);

        // 导出数据到Excel
        $excel_file = $this->import_export_model->export_suppliers($suppliers);
        if (!$excel_file) {
            $this->response([
                'status' => false,
                'message' => '导出供应商数据失败'
            ], 500);
            return;
        }

        // 返回Excel文件URL
        $this->response([
            'status' => true,
            'message' => '导出供应商数据成功',
            'data' => [
                'file_url' => base_url($excel_file)
            ]
        ], 200);
    }

    /**
     * 导入供应商数据
     * POST /api/import/suppliers
     */
    public function import_suppliers_post() {
        // 检查权限
        if (!$this->has_permission('supplier_import')) {
            $this->response([
                'status' => false,
                'message' => '无权限导入供应商数据'
            ], 403);
            return;
        }

        // 检查上传目录
        $upload_dir = './uploads/import/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        // 配置上传参数
        $config['upload_path'] = $upload_dir;
        $config['allowed_types'] = 'xlsx|xls';
        $config['max_size'] = 5120; // 5MB
        $config['file_name'] = 'suppliers_import_' . time();

        $this->load->library('upload', $config);

        // 执行上传
        if (!$this->upload->do_upload('file')) {
            $error = $this->upload->display_errors('', '');
            $this->response([
                'status' => false,
                'message' => $error
            ], 400);
            return;
        }

        // 获取上传文件信息
        $upload_data = $this->upload->data();
        $file_path = $upload_data['full_path'];

        // 导入Excel文件数据
        $result = $this->import_export_model->import_suppliers($file_path);
        if (!$result['success']) {
            $this->response([
                'status' => false,
                'message' => $result['message']
            ], 400);
            return;
        }

        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '导入供应商数据成功',
            'data' => [
                'imported' => $result['imported'],
                'failed' => $result['failed'],
                'errors' => $result['errors']
            ]
        ], 200);
    }
}
