<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 打印控制器
 * 实现各类单据打印功能
 */
class Print extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('print_model');
        $this->load->model('setting_model');
        // 加载其他可能需要的模型
        $this->load->model('sale_model');
        $this->load->model('purchase_model');
        $this->load->model('inventory_model');
        
        // 加载TCPDF库
        require_once APPPATH . 'third_party/tcpdf/tcpdf.php';
    }

    /**
     * 获取打印模板列表
     * GET /api/print/templates
     */
    public function templates_get() {
        // 检查权限
        if (!$this->has_permission('print_manage')) {
            $this->response([
                'status' => false,
                'message' => '无权限管理打印模板'
            ], 403);
            return;
        }

        // 获取模板类型
        $type = $this->get('type');
        
        // 获取模板列表
        $templates = $this->print_model->get_templates($type);
        
        // 返回模板列表
        $this->response([
            'status' => true,
            'data' => $templates
        ], 200);
    }

    /**
     * 获取打印模板详情
     * GET /api/print/template/:id
     */
    public function template_get($id) {
        // 检查权限
        if (!$this->has_permission('print_manage')) {
            $this->response([
                'status' => false,
                'message' => '无权限管理打印模板'
            ], 403);
            return;
        }

        // 获取模板详情
        $template = $this->print_model->get_template_by_id($id);
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '打印模板不存在'
            ], 404);
            return;
        }
        
        // 返回模板详情
        $this->response([
            'status' => true,
            'data' => $template
        ], 200);
    }

    /**
     * 创建打印模板
     * POST /api/print/template
     */
    public function template_post() {
        // 检查权限
        if (!$this->has_permission('print_manage')) {
            $this->response([
                'status' => false,
                'message' => '无权限管理打印模板'
            ], 403);
            return;
        }

        // 获取POST数据
        $data = [
            'name' => $this->post('name'),
            'type' => $this->post('type'),
            'content' => $this->post('content'),
            'is_default' => $this->post('is_default') ? 1 : 0,
            'created_by' => $this->user_id,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // 验证数据
        if (empty($data['name']) || empty($data['type']) || empty($data['content'])) {
            $this->response([
                'status' => false,
                'message' => '请填写完整的模板信息'
            ], 400);
            return;
        }
        
        // 创建模板
        $template_id = $this->print_model->create_template($data);
        if (!$template_id) {
            $this->response([
                'status' => false,
                'message' => '创建打印模板失败'
            ], 500);
            return;
        }
        
        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '创建打印模板成功',
            'data' => [
                'id' => $template_id
            ]
        ], 201);
    }

    /**
     * 更新打印模板
     * PUT /api/print/template/:id
     */
    public function template_put($id) {
        // 检查权限
        if (!$this->has_permission('print_manage')) {
            $this->response([
                'status' => false,
                'message' => '无权限管理打印模板'
            ], 403);
            return;
        }

        // 检查模板是否存在
        $template = $this->print_model->get_template_by_id($id);
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '打印模板不存在'
            ], 404);
            return;
        }
        
        // 获取PUT数据
        $data = [
            'name' => $this->put('name'),
            'type' => $this->put('type'),
            'content' => $this->put('content'),
            'is_default' => $this->put('is_default') ? 1 : 0,
            'updated_by' => $this->user_id,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        // 验证数据
        if (empty($data['name']) || empty($data['type']) || empty($data['content'])) {
            $this->response([
                'status' => false,
                'message' => '请填写完整的模板信息'
            ], 400);
            return;
        }
        
        // 更新模板
        $result = $this->print_model->update_template($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新打印模板失败'
            ], 500);
            return;
        }
        
        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '更新打印模板成功'
        ], 200);
    }

    /**
     * 删除打印模板
     * DELETE /api/print/template/:id
     */
    public function template_delete($id) {
        // 检查权限
        if (!$this->has_permission('print_manage')) {
            $this->response([
                'status' => false,
                'message' => '无权限管理打印模板'
            ], 403);
            return;
        }

        // 检查模板是否存在
        $template = $this->print_model->get_template_by_id($id);
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '打印模板不存在'
            ], 404);
            return;
        }
        
        // 删除模板
        $result = $this->print_model->delete_template($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除打印模板失败'
            ], 500);
            return;
        }
        
        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '删除打印模板成功'
        ], 200);
    }

    /**
     * 设置默认打印模板
     * PUT /api/print/template/:id/default
     */
    public function default_put($id) {
        // 检查权限
        if (!$this->has_permission('print_manage')) {
            $this->response([
                'status' => false,
                'message' => '无权限管理打印模板'
            ], 403);
            return;
        }

        // 检查模板是否存在
        $template = $this->print_model->get_template_by_id($id);
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '打印模板不存在'
            ], 404);
            return;
        }
        
        // 设置为默认模板
        $result = $this->print_model->set_default_template($id, $template['type']);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '设置默认打印模板失败'
            ], 500);
            return;
        }
        
        // 返回成功响应
        $this->response([
            'status' => true,
            'message' => '设置默认打印模板成功'
        ], 200);
    }

    /**
     * 打印销售订单
     * GET /api/print/sale/:id
     */
    public function sale_get($id) {
        // 检查权限
        if (!$this->has_permission('sale_print')) {
            $this->response([
                'status' => false,
                'message' => '无权限打印销售单据'
            ], 403);
            return;
        }

        // 获取销售订单数据
        $sale = $this->sale_model->get_sale_by_id($id);
        if (!$sale) {
            $this->response([
                'status' => false,
                'message' => '销售订单不存在'
            ], 404);
            return;
        }
        
        // 获取默认模板
        $template = $this->print_model->get_default_template('sale');
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '未找到销售单打印模板'
            ], 404);
            return;
        }
        
        // 生成PDF文件
        $pdf_file = $this->generate_pdf($template, $sale, 'sale');
        if (!$pdf_file) {
            $this->response([
                'status' => false,
                'message' => '生成PDF文件失败'
            ], 500);
            return;
        }
        
        // 返回PDF文件URL
        $this->response([
            'status' => true,
            'data' => [
                'file_url' => base_url($pdf_file)
            ]
        ], 200);
    }

    /**
     * 打印采购订单
     * GET /api/print/purchase/:id
     */
    public function purchase_get($id) {
        // 检查权限
        if (!$this->has_permission('purchase_print')) {
            $this->response([
                'status' => false,
                'message' => '无权限打印采购单据'
            ], 403);
            return;
        }

        // 获取采购订单数据
        $purchase = $this->purchase_model->get_purchase_by_id($id);
        if (!$purchase) {
            $this->response([
                'status' => false,
                'message' => '采购订单不存在'
            ], 404);
            return;
        }
        
        // 获取默认模板
        $template = $this->print_model->get_default_template('purchase');
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '未找到采购单打印模板'
            ], 404);
            return;
        }
        
        // 生成PDF文件
        $pdf_file = $this->generate_pdf($template, $purchase, 'purchase');
        if (!$pdf_file) {
            $this->response([
                'status' => false,
                'message' => '生成PDF文件失败'
            ], 500);
            return;
        }
        
        // 返回PDF文件URL
        $this->response([
            'status' => true,
            'data' => [
                'file_url' => base_url($pdf_file)
            ]
        ], 200);
    }

    /**
     * 打印库存单据
     * GET /api/print/inventory/:id
     */
    public function inventory_get($id) {
        // 检查权限
        if (!$this->has_permission('inventory_print')) {
            $this->response([
                'status' => false,
                'message' => '无权限打印库存单据'
            ], 403);
            return;
        }

        // 获取库存单据数据
        $inventory = $this->inventory_model->get_inventory_by_id($id);
        if (!$inventory) {
            $this->response([
                'status' => false,
                'message' => '库存单据不存在'
            ], 404);
            return;
        }
        
        // 获取默认模板
        $template = $this->print_model->get_default_template('inventory');
        if (!$template) {
            $this->response([
                'status' => false,
                'message' => '未找到库存单打印模板'
            ], 404);
            return;
        }
        
        // 生成PDF文件
        $pdf_file = $this->generate_pdf($template, $inventory, 'inventory');
        if (!$pdf_file) {
            $this->response([
                'status' => false,
                'message' => '生成PDF文件失败'
            ], 500);
            return;
        }
        
        // 返回PDF文件URL
        $this->response([
            'status' => true,
            'data' => [
                'file_url' => base_url($pdf_file)
            ]
        ], 200);
    }

    /**
     * 根据模板生成PDF文件
     * @param array $template 模板数据
     * @param array $data 单据数据
     * @param string $type 单据类型
     * @return string|bool PDF文件路径或false
     */
    private function generate_pdf($template, $data, $type) {
        try {
            // 获取公司信息
            $company = $this->setting_model->get_settings_by_group('company');
            
            // 创建PDF对象
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
            
            // 设置文档信息
            $pdf->SetCreator(PDF_CREATOR);
            $pdf->SetAuthor($company['name'] ?? 'ERP System');
            $pdf->SetTitle(($company['name'] ?? 'ERP System') . ' - ' . $this->get_document_title($type));
            $pdf->SetSubject($this->get_document_title($type));
            
            // 设置页眉和页脚
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(true);
            $pdf->setFooterData(array(0,64,0), array(0,64,128));
            $pdf->setFooterFont(Array('stsongstd', '', 8));
            $pdf->SetFooterMargin(10);
            
            // 设置默认等宽字体
            $pdf->SetDefaultMonospacedFont(PDF_FONT_MONOSPACED);
            
            // 设置间距
            $pdf->SetMargins(15, 15, 15);
            
            // 设置自动分页
            $pdf->SetAutoPageBreak(TRUE, 15);
            
            // 设置图像比例因子
            $pdf->setImageScale(PDF_IMAGE_SCALE_RATIO);
            
            // 设置字体
            $pdf->SetFont('stsongstd', '', 10);
            
            // 添加页面
            $pdf->AddPage();
            
            // 处理模板内容
            $html = $this->process_template($template['content'], $data, $company, $type);
            
            // 输出HTML内容
            $pdf->writeHTML($html, true, false, true, false, '');
            
            // 创建保存目录
            $dir = './uploads/print/';
            if (!is_dir($dir)) {
                mkdir($dir, 0777, true);
            }
            
            // 保存PDF文件
            $file_name = $type . '_' . $data['id'] . '_' . date('YmdHis') . '.pdf';
            $file_path = $dir . $file_name;
            $pdf->Output($file_path, 'F');
            
            return 'uploads/print/' . $file_name;
        } catch (Exception $e) {
            log_message('error', 'Generate PDF error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 处理模板内容
     * @param string $template 模板内容
     * @param array $data 单据数据
     * @param array $company 公司信息
     * @param string $type 单据类型
     * @return string 处理后的HTML内容
     */
    private function process_template($template, $data, $company, $type) {
        // 替换公司信息
        foreach ($company as $key => $value) {
            $template = str_replace('{{company.' . $key . '}}', $value, $template);
        }
        
        // 替换单据信息
        $template = str_replace('{{document.title}}', $this->get_document_title($type), $template);
        $template = str_replace('{{document.id}}', $data['id'], $template);
        $template = str_replace('{{document.code}}', $data['code'], $template);
        $template = str_replace('{{document.date}}', date('Y-m-d', strtotime($data['date'])), $template);
        $template = str_replace('{{document.create_time}}', date('Y-m-d H:i:s', strtotime($data['created_at'])), $template);
        
        // 替换其他信息
        if ($type === 'sale') {
            // 销售单特有字段
            $template = str_replace('{{customer.name}}', $data['customer_name'], $template);
            $template = str_replace('{{customer.contact}}', $data['contact_name'], $template);
            $template = str_replace('{{customer.phone}}', $data['contact_phone'], $template);
            $template = str_replace('{{customer.address}}', $data['address'], $template);
        } else if ($type === 'purchase') {
            // 采购单特有字段
            $template = str_replace('{{supplier.name}}', $data['supplier_name'], $template);
            $template = str_replace('{{supplier.contact}}', $data['contact_name'], $template);
            $template = str_replace('{{supplier.phone}}', $data['contact_phone'], $template);
            $template = str_replace('{{supplier.address}}', $data['address'], $template);
        }
        
        // 处理表格数据
        if (isset($data['items']) && is_array($data['items'])) {
            $table_rows = '';
            foreach ($data['items'] as $index => $item) {
                $row = '<tr>';
                $row .= '<td>' . ($index + 1) . '</td>';
                $row .= '<td>' . $item['product_code'] . '</td>';
                $row .= '<td>' . $item['product_name'] . '</td>';
                $row .= '<td>' . $item['specification'] . '</td>';
                $row .= '<td>' . $item['unit'] . '</td>';
                $row .= '<td>' . $item['quantity'] . '</td>';
                $row .= '<td>' . $item['price'] . '</td>';
                $row .= '<td>' . $item['amount'] . '</td>';
                $row .= '<td>' . $item['remark'] . '</td>';
                $row .= '</tr>';
                $table_rows .= $row;
            }
            $template = str_replace('{{table.rows}}', $table_rows, $template);
        }
        
        // 替换合计信息
        $template = str_replace('{{document.total_amount}}', $data['total_amount'], $template);
        $template = str_replace('{{document.discount_amount}}', $data['discount_amount'], $template);
        $template = str_replace('{{document.tax_amount}}', $data['tax_amount'], $template);
        $template = str_replace('{{document.amount}}', $data['amount'], $template);
        
        // 替换经办人信息
        $template = str_replace('{{document.created_by}}', $data['created_by_name'], $template);
        
        // 替换其他可能的变量
        $template = str_replace('{{document.remark}}', $data['remark'], $template);
        
        return $template;
    }

    /**
     * 获取单据标题
     * @param string $type 单据类型
     * @return string 单据标题
     */
    private function get_document_title($type) {
        switch ($type) {
            case 'sale':
                return '销售单';
            case 'purchase':
                return '采购单';
            case 'inventory':
                return '库存单';
            default:
                return '单据';
        }
    }
}
