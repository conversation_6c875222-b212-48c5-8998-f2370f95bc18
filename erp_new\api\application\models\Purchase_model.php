<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 采购模型类
 * 处理采购相关的数据库操作
 */
class Purchase_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取采购订单列表
     * @param array $params 查询参数
     * @return array 采购订单列表
     */
    public function get_orders($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('po.*, s.name as supplier_name, w.name as warehouse_name, u.name as created_by_name');
        $this->db->from('purchase_orders po');
        $this->db->join('suppliers s', 'po.supplier_id = s.id', 'left');
        $this->db->join('warehouses w', 'po.warehouse_id = w.id', 'left');
        $this->db->join('users u', 'po.created_by = u.id', 'left');
        $this->db->where('po.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('po.order_no', $keyword);
            $this->db->or_like('s.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('po.supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('po.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('po.order_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('po.order_date <=', $params['end_date']);
        }
        
        $this->db->order_by('po.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取采购订单总数
     * @param array $params 查询参数
     * @return int 采购订单总数
     */
    public function count_orders($params = []) {
        $this->db->from('purchase_orders po');
        $this->db->join('suppliers s', 'po.supplier_id = s.id', 'left');
        $this->db->where('po.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('po.order_no', $keyword);
            $this->db->or_like('s.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('po.supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('po.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('po.order_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('po.order_date <=', $params['end_date']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取采购订单详情
     * @param int $id 采购订单ID
     * @return array|null 采购订单详情
     */
    public function get_order_by_id($id) {
        $this->db->select('po.*, s.name as supplier_name, w.name as warehouse_name, u.name as created_by_name');
        $this->db->from('purchase_orders po');
        $this->db->join('suppliers s', 'po.supplier_id = s.id', 'left');
        $this->db->join('warehouses w', 'po.warehouse_id = w.id', 'left');
        $this->db->join('users u', 'po.created_by = u.id', 'left');
        $this->db->where('po.id', $id);
        $this->db->where('po.is_deleted', 0);
        
        $query = $this->db->get();
        $order = $query->row_array();
        
        if ($order) {
            // 获取订单详情项
            $this->db->select('pod.*, p.name as product_name, p.code as product_code, p.specification');
            $this->db->from('purchase_order_details pod');
            $this->db->join('products p', 'pod.product_id = p.id', 'left');
            $this->db->where('pod.order_id', $id);
            
            $query = $this->db->get();
            $order['details'] = $query->result_array();
        }
        
        return $order;
    }
    
    /**
     * 创建采购订单
     * @param array $data 采购订单数据
     * @param array $details 采购订单详情数据
     * @return int|bool 采购订单ID或false
     */
    public function create_order($data, $details) {
        $this->db->trans_start();
        
        // 插入采购订单
        $this->db->insert('purchase_orders', $data);
        $order_id = $this->db->insert_id();
        
        // 插入采购订单详情
        if ($order_id && is_array($details) && count($details) > 0) {
            foreach ($details as $detail) {
                $detail['order_id'] = $order_id;
                $this->db->insert('purchase_order_details', $detail);
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : $order_id;
    }
    
    /**
     * 更新采购订单
     * @param int $id 采购订单ID
     * @param array $data 采购订单数据
     * @param array $details 采购订单详情数据
     * @return bool 是否成功
     */
    public function update_order($id, $data, $details = null) {
        $this->db->trans_start();
        
        // 更新采购订单
        $this->db->where('id', $id);
        $this->db->update('purchase_orders', $data);
        
        // 如果提供了详情数据，则更新详情
        if ($details !== null) {
            // 删除原有的详情
            $this->db->where('order_id', $id);
            $this->db->delete('purchase_order_details');
            
            // 插入新的详情
            if (is_array($details) && count($details) > 0) {
                foreach ($details as $detail) {
                    $detail['order_id'] = $id;
                    $this->db->insert('purchase_order_details', $detail);
                }
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 更新采购订单状态
     * @param int $id 采购订单ID
     * @param string $status 状态
     * @param int $user_id 操作用户ID
     * @return bool 是否成功
     */
    public function update_order_status($id, $status, $user_id) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $user_id
        ];
        
        if ($status == 'confirmed') {
            $data['confirmed_by'] = $user_id;
            $data['confirmed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'completed') {
            $data['completed_by'] = $user_id;
            $data['completed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'cancelled') {
            $data['cancelled_by'] = $user_id;
            $data['cancelled_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->where('id', $id);
        return $this->db->update('purchase_orders', $data);
    }
    
    /**
     * 删除采购订单（软删除）
     * @param int $id 采购订单ID
     * @return bool 是否成功
     */
    public function delete_order($id) {
        $this->db->where('id', $id);
        return $this->db->update('purchase_orders', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取采购入库单列表
     * @param array $params 查询参数
     * @return array 采购入库单列表
     */
    public function get_receives($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('pr.*, po.order_no, s.name as supplier_name, w.name as warehouse_name, u.name as created_by_name');
        $this->db->from('purchase_receives pr');
        $this->db->join('purchase_orders po', 'pr.order_id = po.id', 'left');
        $this->db->join('suppliers s', 'pr.supplier_id = s.id', 'left');
        $this->db->join('warehouses w', 'pr.warehouse_id = w.id', 'left');
        $this->db->join('users u', 'pr.created_by = u.id', 'left');
        $this->db->where('pr.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('pr.receive_no', $keyword);
            $this->db->or_like('po.order_no', $keyword);
            $this->db->or_like('s.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('pr.supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['warehouse_id']) && $params['warehouse_id']) {
            $this->db->where('pr.warehouse_id', $params['warehouse_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('pr.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('pr.receive_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('pr.receive_date <=', $params['end_date']);
        }
        
        $this->db->order_by('pr.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取采购入库单总数
     * @param array $params 查询参数
     * @return int 采购入库单总数
     */
    public function count_receives($params = []) {
        $this->db->from('purchase_receives pr');
        $this->db->join('purchase_orders po', 'pr.order_id = po.id', 'left');
        $this->db->join('suppliers s', 'pr.supplier_id = s.id', 'left');
        $this->db->where('pr.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('pr.receive_no', $keyword);
            $this->db->or_like('po.order_no', $keyword);
            $this->db->or_like('s.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('pr.supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['warehouse_id']) && $params['warehouse_id']) {
            $this->db->where('pr.warehouse_id', $params['warehouse_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('pr.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('pr.receive_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('pr.receive_date <=', $params['end_date']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取采购入库单详情
     * @param int $id 采购入库单ID
     * @return array|null 采购入库单详情
     */
    public function get_receive_by_id($id) {
        $this->db->select('pr.*, po.order_no, s.name as supplier_name, w.name as warehouse_name, u.name as created_by_name');
        $this->db->from('purchase_receives pr');
        $this->db->join('purchase_orders po', 'pr.order_id = po.id', 'left');
        $this->db->join('suppliers s', 'pr.supplier_id = s.id', 'left');
        $this->db->join('warehouses w', 'pr.warehouse_id = w.id', 'left');
        $this->db->join('users u', 'pr.created_by = u.id', 'left');
        $this->db->where('pr.id', $id);
        $this->db->where('pr.is_deleted', 0);
        
        $query = $this->db->get();
        $receive = $query->row_array();
        
        if ($receive) {
            // 获取入库单详情项
            $this->db->select('prd.*, p.name as product_name, p.code as product_code, p.specification');
            $this->db->from('purchase_receive_details prd');
            $this->db->join('products p', 'prd.product_id = p.id', 'left');
            $this->db->where('prd.receive_id', $id);
            
            $query = $this->db->get();
            $receive['details'] = $query->result_array();
        }
        
        return $receive;
    }
    
    /**
     * 创建采购入库单
     * @param array $data 采购入库单数据
     * @param array $details 采购入库单详情数据
     * @return int|bool 采购入库单ID或false
     */
    public function create_receive($data, $details) {
        $this->db->trans_start();
        
        // 插入采购入库单
        $this->db->insert('purchase_receives', $data);
        $receive_id = $this->db->insert_id();
        
        // 插入采购入库单详情
        if ($receive_id && is_array($details) && count($details) > 0) {
            foreach ($details as $detail) {
                $detail['receive_id'] = $receive_id;
                $this->db->insert('purchase_receive_details', $detail);
                
                // 更新库存
                if ($data['status'] == 'confirmed') {
                    $this->update_stock($data['warehouse_id'], $detail['product_id'], $detail['quantity']);
                }
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : $receive_id;
    }
    
    /**
     * 更新采购入库单
     * @param int $id 采购入库单ID
     * @param array $data 采购入库单数据
     * @param array $details 采购入库单详情数据
     * @return bool 是否成功
     */
    public function update_receive($id, $data, $details = null) {
        $this->db->trans_start();
        
        // 获取原有入库单数据
        $old_receive = $this->get_receive_by_id($id);
        
        // 更新采购入库单
        $this->db->where('id', $id);
        $this->db->update('purchase_receives', $data);
        
        // 如果提供了详情数据，则更新详情
        if ($details !== null) {
            // 如果之前的状态是已确认，需要先回滚库存
            if ($old_receive['status'] == 'confirmed') {
                foreach ($old_receive['details'] as $old_detail) {
                    $this->update_stock($old_receive['warehouse_id'], $old_detail['product_id'], -$old_detail['quantity']);
                }
            }
            
            // 删除原有的详情
            $this->db->where('receive_id', $id);
            $this->db->delete('purchase_receive_details');
            
            // 插入新的详情
            if (is_array($details) && count($details) > 0) {
                foreach ($details as $detail) {
                    $detail['receive_id'] = $id;
                    $this->db->insert('purchase_receive_details', $detail);
                    
                    // 如果当前状态是已确认，需要更新库存
                    if (isset($data['status']) && $data['status'] == 'confirmed') {
                        $this->update_stock($data['warehouse_id'] ?? $old_receive['warehouse_id'], $detail['product_id'], $detail['quantity']);
                    }
                }
            }
        } else if (isset($data['status']) && $data['status'] == 'confirmed' && $old_receive['status'] != 'confirmed') {
            // 如果只是状态从非确认变为已确认，需要更新库存
            foreach ($old_receive['details'] as $detail) {
                $this->update_stock($old_receive['warehouse_id'], $detail['product_id'], $detail['quantity']);
            }
        } else if (isset($data['status']) && $data['status'] != 'confirmed' && $old_receive['status'] == 'confirmed') {
            // 如果状态从已确认变为非确认，需要回滚库存
            foreach ($old_receive['details'] as $detail) {
                $this->update_stock($old_receive['warehouse_id'], $detail['product_id'], -$detail['quantity']);
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 更新采购入库单状态
     * @param int $id 采购入库单ID
     * @param string $status 状态
     * @param int $user_id 操作用户ID
     * @return bool 是否成功
     */
    public function update_receive_status($id, $status, $user_id) {
        // 获取原有入库单数据
        $old_receive = $this->get_receive_by_id($id);
        
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $user_id
        ];
        
        if ($status == 'confirmed') {
            $data['confirmed_by'] = $user_id;
            $data['confirmed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'cancelled') {
            $data['cancelled_by'] = $user_id;
            $data['cancelled_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->trans_start();
        
        // 更新入库单状态
        $this->db->where('id', $id);
        $this->db->update('purchase_receives', $data);
        
        // 如果状态从非确认变为已确认，需要更新库存
        if ($status == 'confirmed' && $old_receive['status'] != 'confirmed') {
            foreach ($old_receive['details'] as $detail) {
                $this->update_stock($old_receive['warehouse_id'], $detail['product_id'], $detail['quantity']);
            }
        } 
        // 如果状态从已确认变为已取消，需要回滚库存
        else if ($status == 'cancelled' && $old_receive['status'] == 'confirmed') {
            foreach ($old_receive['details'] as $detail) {
                $this->update_stock($old_receive['warehouse_id'], $detail['product_id'], -$detail['quantity']);
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 删除采购入库单（软删除）
     * @param int $id 采购入库单ID
     * @return bool 是否成功
     */
    public function delete_receive($id) {
        $this->db->where('id', $id);
        return $this->db->update('purchase_receives', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 更新库存
     * @param int $warehouse_id 仓库ID
     * @param int $product_id 产品ID
     * @param float $quantity 数量变化
     * @return bool 是否成功
     */
    private function update_stock($warehouse_id, $product_id, $quantity) {
        // 检查是否存在该仓库的该产品库存记录
        $this->db->from('inventory');
        $this->db->where('warehouse_id', $warehouse_id);
        $this->db->where('product_id', $product_id);
        
        $query = $this->db->get();
        $inventory = $query->row_array();
        
        if ($inventory) {
            // 更新库存
            $this->db->where('id', $inventory['id']);
            $this->db->set('quantity', 'quantity + ' . $quantity, FALSE);
            $this->db->update('inventory');
        } else {
            // 创建库存记录
            $this->db->insert('inventory', [
                'warehouse_id' => $warehouse_id,
                'product_id' => $product_id,
                'quantity' => $quantity,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        return true;
    }
}
