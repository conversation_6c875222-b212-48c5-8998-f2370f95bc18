<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 用户模型类
 * 处理用户相关的数据库操作
 */
class User_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 检查用户登录
     * @param string $username 用户名
     * @param string $password 密码
     * @return array|bool 用户信息或false
     */
    public function check_login($username, $password) {
        $this->db->select('u.*, r.name as role_name');
        $this->db->from('users u');
        $this->db->join('roles r', 'u.role_id = r.id', 'left');
        $this->db->where('u.username', $username);
        $this->db->where('u.is_deleted', 0);
        
        $query = $this->db->get();
        $user = $query->row_array();
        
        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }
        
        return false;
    }
    
    /**
     * 更新用户最后登录时间
     * @param int $user_id 用户ID
     * @return bool 是否成功
     */
    public function update_last_login($user_id) {
        $this->db->where('id', $user_id);
        return $this->db->update('users', ['last_login' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取用户列表
     * @param array $params 查询参数
     * @return array 用户列表
     */
    public function get_users($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('u.id, u.username, u.name, u.email, u.phone, u.status, u.last_login, u.created_at, r.name as role_name, r.id as role_id');
        $this->db->from('users u');
        $this->db->join('roles r', 'u.role_id = r.id', 'left');
        $this->db->where('u.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('u.username', $keyword);
            $this->db->or_like('u.name', $keyword);
            $this->db->or_like('u.email', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['role_id']) && $params['role_id']) {
            $this->db->where('u.role_id', $params['role_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('u.status', $params['status']);
        }
        
        $this->db->order_by('u.id', 'ASC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取用户总数
     * @param array $params 查询参数
     * @return int 用户总数
     */
    public function count_users($params = []) {
        $this->db->from('users u');
        $this->db->where('u.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('u.username', $keyword);
            $this->db->or_like('u.name', $keyword);
            $this->db->or_like('u.email', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['role_id']) && $params['role_id']) {
            $this->db->where('u.role_id', $params['role_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('u.status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取用户信息
     * @param int $id 用户ID
     * @return array|null 用户信息
     */
    public function get_user_by_id($id) {
        $this->db->select('u.*, r.name as role_name');
        $this->db->from('users u');
        $this->db->join('roles r', 'u.role_id = r.id', 'left');
        $this->db->where('u.id', $id);
        $this->db->where('u.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 根据用户名获取用户信息
     * @param string $username 用户名
     * @return array|null 用户信息
     */
    public function get_user_by_username($username) {
        $this->db->select('u.*, r.name as role_name');
        $this->db->from('users u');
        $this->db->join('roles r', 'u.role_id = r.id', 'left');
        $this->db->where('u.username', $username);
        $this->db->where('u.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 检查用户名是否存在
     * @param string $username 用户名
     * @param int $exclude_id 排除的用户ID
     * @return bool 是否存在
     */
    public function check_username_exists($username, $exclude_id = 0) {
        $this->db->from('users');
        $this->db->where('username', $username);
        $this->db->where('is_deleted', 0);
        
        if ($exclude_id > 0) {
            $this->db->where('id !=', $exclude_id);
        }
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 创建用户
     * @param array $data 用户数据
     * @return int|bool 用户ID或false
     */
    public function create_user($data) {
        // 密码加密
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $this->db->insert('users', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新用户
     * @param int $id 用户ID
     * @param array $data 用户数据
     * @return bool 是否成功
     */
    public function update_user($id, $data) {
        $this->db->where('id', $id);
        return $this->db->update('users', $data);
    }
    
    /**
     * 更新用户密码
     * @param int $id 用户ID
     * @param string $password 新密码
     * @return bool 是否成功
     */
    public function update_password($id, $password) {
        $this->db->where('id', $id);
        return $this->db->update('users', [
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 删除用户（软删除）
     * @param int $id 用户ID
     * @return bool 是否成功
     */
    public function delete_user($id) {
        $this->db->where('id', $id);
        return $this->db->update('users', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
}
