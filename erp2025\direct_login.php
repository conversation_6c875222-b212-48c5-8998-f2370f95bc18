<?php
// 直接登录绕过系统脚本
// 此脚本用于解决错误模板路径问题和token验证问题

// 定义基础路径常量
define('BASEPATH', './system/');

// 包含必要的数据库配置
require_once './application/config/database.php';

// 连接到数据库
$db_config = $db['default'];
$host = $db_config['hostname'];
$username = $db_config['username'];
$password = $db_config['password'];
$database = $db_config['database'];
$port = $db_config['port'];

// 创建数据库连接
$conn = mysqli_connect($host, $username, $password, $database, $port);
if (!$conn) {
    die("数据库连接失败: " . mysqli_connect_error());
}

// 处理登录请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['userpwd']) ? trim($_POST['userpwd']) : '';
    
    if (empty($username)) {
        die('用户名不能为空');
    }
    
    if (empty($password)) {
        die('密码不能为空');
    }
    
    // 查询用户
    $username = mysqli_real_escape_string($conn, $username);
    $query = "SELECT * FROM ci_admin WHERE (username='$username' OR mobile='$username')";
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        
        if ($user['status'] != 1) {
            die('账号被锁定');
        }
        
        if ($user['userpwd'] == md5($password)) {
            // 登录成功，设置cookie和session
            session_start();
            $_SESSION['uid'] = $user['uid'];
            $_SESSION['name'] = $user['name'];
            $_SESSION['roleid'] = $user['roleid'];
            $_SESSION['username'] = $user['username'];
            
            // 设置cookie
            setcookie('username', $username, time() + 3600000, '/');
            setcookie('userpwd', $password, time() + 3600000, '/');
            
            // 记录登录成功日志
            $log_query = "INSERT INTO ci_log (name, operation, content, create_time) VALUES ('{$user['username']}', '登录', '登录成功', NOW())";
            mysqli_query($conn, $log_query);
            
            // 重定向到主页
            header("Location: index.php/home/<USER>");
            exit;
        }
    }
    
    die('账号或密码错误');
}

// 显示登录页面
?>
<!DOCTYPE html>
<html>
    <head>
        <title>云进销存系统 - 直接登录</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <style>
            body { font-family: Arial, sans-serif; background-color: #f0f0f0; margin: 0; padding: 0; }
            .container { max-width: 400px; margin: 100px auto; padding: 20px; background-color: #fff; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
            h2 { text-align: center; color: #333; }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input[type="text"], input[type="password"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 3px; box-sizing: border-box; }
            button { width: 100%; padding: 10px; background-color: #4CAF50; color: white; border: none; border-radius: 3px; cursor: pointer; }
            button:hover { background-color: #45a049; }
            .error { color: red; margin-bottom: 15px; }
            .note { margin-top: 20px; font-size: 12px; color: #666; text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>系统直接登录</h2>
            <?php if (isset($error)): ?>
                <div class="error"><?php echo $error; ?></div>
            <?php endif; ?>
            <form method="POST">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label for="userpwd">密码</label>
                    <input type="password" id="userpwd" name="userpwd" placeholder="请输入密码">
                </div>
                <button type="submit">登录</button>
            </form>
            <div class="note">此页面是临时登录页面，用于解决系统错误问题</div>
        </div>
    </body>
</html>
