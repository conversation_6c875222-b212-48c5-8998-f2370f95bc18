<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 财务模型类
 * 处理财务相关的数据库操作
 */
class Finance_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取收款单列表
     * @param array $params 查询参数
     * @return array 收款单列表
     */
    public function get_receipts($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('r.*, c.name as customer_name, so.order_no as order_no, u.name as created_by_name');
        $this->db->from('receipts r');
        $this->db->join('customers c', 'r.customer_id = c.id', 'left');
        $this->db->join('sales_orders so', 'r.order_id = so.id', 'left');
        $this->db->join('users u', 'r.created_by = u.id', 'left');
        $this->db->where('r.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('r.receipt_no', $keyword);
            $this->db->or_like('c.name', $keyword);
            $this->db->or_like('so.order_no', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('r.customer_id', $params['customer_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('r.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('r.receipt_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('r.receipt_date <=', $params['end_date']);
        }
        
        $this->db->order_by('r.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取收款单总数
     * @param array $params 查询参数
     * @return int 收款单总数
     */
    public function count_receipts($params = []) {
        $this->db->from('receipts r');
        $this->db->join('customers c', 'r.customer_id = c.id', 'left');
        $this->db->join('sales_orders so', 'r.order_id = so.id', 'left');
        $this->db->where('r.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('r.receipt_no', $keyword);
            $this->db->or_like('c.name', $keyword);
            $this->db->or_like('so.order_no', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('r.customer_id', $params['customer_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('r.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('r.receipt_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('r.receipt_date <=', $params['end_date']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取收款单详情
     * @param int $id 收款单ID
     * @return array|null 收款单详情
     */
    public function get_receipt_by_id($id) {
        $this->db->select('r.*, c.name as customer_name, so.order_no as order_no, u.name as created_by_name');
        $this->db->from('receipts r');
        $this->db->join('customers c', 'r.customer_id = c.id', 'left');
        $this->db->join('sales_orders so', 'r.order_id = so.id', 'left');
        $this->db->join('users u', 'r.created_by = u.id', 'left');
        $this->db->where('r.id', $id);
        $this->db->where('r.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 创建收款单
     * @param array $data 收款单数据
     * @return int|bool 收款单ID或false
     */
    public function create_receipt($data) {
        $this->db->insert('receipts', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新收款单状态
     * @param int $id 收款单ID
     * @param string $status 状态
     * @param int $user_id 操作用户ID
     * @return bool 是否成功
     */
    public function update_receipt_status($id, $status, $user_id) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $user_id
        ];
        
        if ($status == 'confirmed') {
            $data['confirmed_by'] = $user_id;
            $data['confirmed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'cancelled') {
            $data['cancelled_by'] = $user_id;
            $data['cancelled_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->where('id', $id);
        return $this->db->update('receipts', $data);
    }
    
    /**
     * 删除收款单（软删除）
     * @param int $id 收款单ID
     * @return bool 是否成功
     */
    public function delete_receipt($id) {
        $this->db->where('id', $id);
        return $this->db->update('receipts', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取付款单列表
     * @param array $params 查询参数
     * @return array 付款单列表
     */
    public function get_payments($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('p.*, s.name as supplier_name, po.order_no as order_no, u.name as created_by_name');
        $this->db->from('payments p');
        $this->db->join('suppliers s', 'p.supplier_id = s.id', 'left');
        $this->db->join('purchase_orders po', 'p.order_id = po.id', 'left');
        $this->db->join('users u', 'p.created_by = u.id', 'left');
        $this->db->where('p.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('p.payment_no', $keyword);
            $this->db->or_like('s.name', $keyword);
            $this->db->or_like('po.order_no', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('p.supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('p.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('p.payment_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('p.payment_date <=', $params['end_date']);
        }
        
        $this->db->order_by('p.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取付款单总数
     * @param array $params 查询参数
     * @return int 付款单总数
     */
    public function count_payments($params = []) {
        $this->db->from('payments p');
        $this->db->join('suppliers s', 'p.supplier_id = s.id', 'left');
        $this->db->join('purchase_orders po', 'p.order_id = po.id', 'left');
        $this->db->where('p.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('p.payment_no', $keyword);
            $this->db->or_like('s.name', $keyword);
            $this->db->or_like('po.order_no', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('p.supplier_id', $params['supplier_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('p.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('p.payment_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('p.payment_date <=', $params['end_date']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 根据ID获取付款单详情
     * @param int $id 付款单ID
     * @return array|null 付款单详情
     */
    public function get_payment_by_id($id) {
        $this->db->select('p.*, s.name as supplier_name, po.order_no as order_no, u.name as created_by_name');
        $this->db->from('payments p');
        $this->db->join('suppliers s', 'p.supplier_id = s.id', 'left');
        $this->db->join('purchase_orders po', 'p.order_id = po.id', 'left');
        $this->db->join('users u', 'p.created_by = u.id', 'left');
        $this->db->where('p.id', $id);
        $this->db->where('p.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 创建付款单
     * @param array $data 付款单数据
     * @return int|bool 付款单ID或false
     */
    public function create_payment($data) {
        $this->db->insert('payments', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新付款单状态
     * @param int $id 付款单ID
     * @param string $status 状态
     * @param int $user_id 操作用户ID
     * @return bool 是否成功
     */
    public function update_payment_status($id, $status, $user_id) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $user_id
        ];
        
        if ($status == 'confirmed') {
            $data['confirmed_by'] = $user_id;
            $data['confirmed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'cancelled') {
            $data['cancelled_by'] = $user_id;
            $data['cancelled_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->where('id', $id);
        return $this->db->update('payments', $data);
    }
    
    /**
     * 删除付款单（软删除）
     * @param int $id 付款单ID
     * @return bool 是否成功
     */
    public function delete_payment($id) {
        $this->db->where('id', $id);
        return $this->db->update('payments', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取客户应收账款报表
     * @param array $params 查询参数
     * @return array 应收账款报表
     */
    public function get_receivables($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        $as_of_date = isset($params['as_of_date']) ? $params['as_of_date'] : date('Y-m-d');
        
        $this->db->select('c.id, c.name, c.code, c.contact_name, c.contact_phone, 
                          SUM(CASE WHEN so.status = "completed" THEN so.grand_total ELSE 0 END) as total_sales,
                          SUM(CASE WHEN r.status = "confirmed" THEN r.amount ELSE 0 END) as total_receipts,
                          SUM(CASE WHEN so.status = "completed" THEN so.grand_total ELSE 0 END) - 
                          SUM(CASE WHEN r.status = "confirmed" THEN r.amount ELSE 0 END) as balance');
        $this->db->from('customers c');
        $this->db->join('sales_orders so', 'c.id = so.customer_id AND so.is_deleted = 0 AND so.order_date <= "' . $as_of_date . '"', 'left');
        $this->db->join('receipts r', 'c.id = r.customer_id AND r.is_deleted = 0 AND r.receipt_date <= "' . $as_of_date . '"', 'left');
        $this->db->where('c.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('c.name', $keyword);
            $this->db->or_like('c.code', $keyword);
            $this->db->or_like('c.contact_name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('c.id', $params['customer_id']);
        }
        
        // 只显示有欠款的客户
        if (isset($params['overdue']) && $params['overdue']) {
            $this->db->having('balance > 0');
        }
        
        $this->db->group_by('c.id');
        $this->db->order_by('balance', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取客户应收账款报表总数
     * @param array $params 查询参数
     * @return int 应收账款报表总数
     */
    public function count_receivables($params = []) {
        $as_of_date = isset($params['as_of_date']) ? $params['as_of_date'] : date('Y-m-d');
        
        $this->db->select('c.id, 
                          SUM(CASE WHEN so.status = "completed" THEN so.grand_total ELSE 0 END) - 
                          SUM(CASE WHEN r.status = "confirmed" THEN r.amount ELSE 0 END) as balance');
        $this->db->from('customers c');
        $this->db->join('sales_orders so', 'c.id = so.customer_id AND so.is_deleted = 0 AND so.order_date <= "' . $as_of_date . '"', 'left');
        $this->db->join('receipts r', 'c.id = r.customer_id AND r.is_deleted = 0 AND r.receipt_date <= "' . $as_of_date . '"', 'left');
        $this->db->where('c.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('c.name', $keyword);
            $this->db->or_like('c.code', $keyword);
            $this->db->or_like('c.contact_name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['customer_id']) && $params['customer_id']) {
            $this->db->where('c.id', $params['customer_id']);
        }
        
        $this->db->group_by('c.id');
        
        // 只显示有欠款的客户
        if (isset($params['overdue']) && $params['overdue']) {
            $this->db->having('balance > 0');
        }
        
        $query = $this->db->get();
        return $query->num_rows();
    }
    
    /**
     * 获取供应商应付账款报表
     * @param array $params 查询参数
     * @return array 应付账款报表
     */
    public function get_payables($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        $as_of_date = isset($params['as_of_date']) ? $params['as_of_date'] : date('Y-m-d');
        
        $this->db->select('s.id, s.name, s.code, s.contact_name, s.contact_phone, 
                          SUM(CASE WHEN po.status = "completed" THEN po.grand_total ELSE 0 END) as total_purchases,
                          SUM(CASE WHEN p.status = "confirmed" THEN p.amount ELSE 0 END) as total_payments,
                          SUM(CASE WHEN po.status = "completed" THEN po.grand_total ELSE 0 END) - 
                          SUM(CASE WHEN p.status = "confirmed" THEN p.amount ELSE 0 END) as balance');
        $this->db->from('suppliers s');
        $this->db->join('purchase_orders po', 's.id = po.supplier_id AND po.is_deleted = 0 AND po.order_date <= "' . $as_of_date . '"', 'left');
        $this->db->join('payments p', 's.id = p.supplier_id AND p.is_deleted = 0 AND p.payment_date <= "' . $as_of_date . '"', 'left');
        $this->db->where('s.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('s.name', $keyword);
            $this->db->or_like('s.code', $keyword);
            $this->db->or_like('s.contact_name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('s.id', $params['supplier_id']);
        }
        
        // 只显示有欠款的供应商
        if (isset($params['overdue']) && $params['overdue']) {
            $this->db->having('balance > 0');
        }
        
        $this->db->group_by('s.id');
        $this->db->order_by('balance', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取供应商应付账款报表总数
     * @param array $params 查询参数
     * @return int 应付账款报表总数
     */
    public function count_payables($params = []) {
        $as_of_date = isset($params['as_of_date']) ? $params['as_of_date'] : date('Y-m-d');
        
        $this->db->select('s.id, 
                          SUM(CASE WHEN po.status = "completed" THEN po.grand_total ELSE 0 END) - 
                          SUM(CASE WHEN p.status = "confirmed" THEN p.amount ELSE 0 END) as balance');
        $this->db->from('suppliers s');
        $this->db->join('purchase_orders po', 's.id = po.supplier_id AND po.is_deleted = 0 AND po.order_date <= "' . $as_of_date . '"', 'left');
        $this->db->join('payments p', 's.id = p.supplier_id AND p.is_deleted = 0 AND p.payment_date <= "' . $as_of_date . '"', 'left');
        $this->db->where('s.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('s.name', $keyword);
            $this->db->or_like('s.code', $keyword);
            $this->db->or_like('s.contact_name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['supplier_id']) && $params['supplier_id']) {
            $this->db->where('s.id', $params['supplier_id']);
        }
        
        $this->db->group_by('s.id');
        
        // 只显示有欠款的供应商
        if (isset($params['overdue']) && $params['overdue']) {
            $this->db->having('balance > 0');
        }
        
        $query = $this->db->get();
        return $query->num_rows();
    }
    
    /**
     * 获取收支汇总报表
     * @param string $start_date 开始日期
     * @param string $end_date 结束日期
     * @param string $group_by 分组方式 (day/month/year)
     * @return array 收支汇总报表
     */
    public function get_income_expense_summary($start_date, $end_date, $group_by = 'day') {
        // 定义分组字段
        $date_format = '';
        switch ($group_by) {
            case 'day':
                $date_format = '%Y-%m-%d';
                break;
            case 'month':
                $date_format = '%Y-%m';
                break;
            case 'year':
                $date_format = '%Y';
                break;
            default:
                $date_format = '%Y-%m-%d';
        }
        
        // 获取收入数据
        $this->db->select('DATE_FORMAT(r.receipt_date, "' . $date_format . '") as date, SUM(r.amount) as income');
        $this->db->from('receipts r');
        $this->db->where('r.is_deleted', 0);
        $this->db->where('r.status', 'confirmed');
        $this->db->where('r.receipt_date >=', $start_date);
        $this->db->where('r.receipt_date <=', $end_date);
        $this->db->group_by('date');
        
        $query_income = $this->db->get();
        $income_data = $query_income->result_array();
        
        // 获取支出数据
        $this->db->select('DATE_FORMAT(p.payment_date, "' . $date_format . '") as date, SUM(p.amount) as expense');
        $this->db->from('payments p');
        $this->db->where('p.is_deleted', 0);
        $this->db->where('p.status', 'confirmed');
        $this->db->where('p.payment_date >=', $start_date);
        $this->db->where('p.payment_date <=', $end_date);
        $this->db->group_by('date');
        
        $query_expense = $this->db->get();
        $expense_data = $query_expense->result_array();
        
        // 合并数据
        $result = [];
        
        // 创建日期数组
        $date_period = [];
        $current_date = new DateTime($start_date);
        $end_date_obj = new DateTime($end_date);
        
        switch ($group_by) {
            case 'day':
                while ($current_date <= $end_date_obj) {
                    $date_period[] = $current_date->format('Y-m-d');
                    $current_date->add(new DateInterval('P1D'));
                }
                break;
            case 'month':
                while ($current_date <= $end_date_obj) {
                    $date_period[] = $current_date->format('Y-m');
                    $current_date->add(new DateInterval('P1M'));
                }
                break;
            case 'year':
                while ($current_date <= $end_date_obj) {
                    $date_period[] = $current_date->format('Y');
                    $current_date->add(new DateInterval('P1Y'));
                }
                break;
        }
        
        foreach ($date_period as $date) {
            $income = 0;
            $expense = 0;
            
            // 查找收入
            foreach ($income_data as $income_item) {
                if ($income_item['date'] === $date) {
                    $income = floatval($income_item['income']);
                    break;
                }
            }
            
            // 查找支出
            foreach ($expense_data as $expense_item) {
                if ($expense_item['date'] === $date) {
                    $expense = floatval($expense_item['expense']);
                    break;
                }
            }
            
            $result[] = [
                'date' => $date,
                'income' => $income,
                'expense' => $expense,
                'profit' => $income - $expense
            ];
        }
        
        return $result;
    }
}
