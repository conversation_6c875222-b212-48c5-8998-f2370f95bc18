<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 认证模型
 *
 * 处理用户登录和令牌管理
 */
class Auth_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 用户登录
     *
     * @param string $username 用户名
     * @param string $password 密码
     * @return array 登录结果
     */
    public function login($username, $password) {
        try {
            // 查询用户（支持用户名或手机号登录）
            $this->db->where("(username = '{$username}' OR mobile = '{$username}')");
            $this->db->where('isDelete', 0);
            $query = $this->db->get('admin');
            $user = $query->row_array();

            if (!$user) {
                return [
                    'status' => false,
                    'message' => '用户不存在'
                ];
            }

            // 检查用户状态
            if ($user['status'] != 1) {
                return [
                    'status' => false,
                    'message' => '账号被锁定'
                ];
            }

            // 验证密码
            if ($user['userpwd'] !== md5($password)) {
                return [
                    'status' => false,
                    'message' => '密码错误'
                ];
            }

            // 获取用户权限
            $permissions = $this->_get_user_permissions($user['roleid']);

            // 记录登录日志
            $this->_log_login($user['username']);

            return [
                'status' => true,
                'message' => '登录成功',
                'data' => $user,
                'permissions' => $permissions
            ];

        } catch (Exception $e) {
            log_message('error', '登录模型错误: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => '登录失败，请稍后重试'
            ];
        }
    }

    /**
     * 创建访问令牌
     *
     * @param int $user_id 用户ID
     * @return array 令牌数据
     */
    public function create_token($user_id) {
        try {
            // 生成随机令牌
            $token = bin2hex(random_bytes(32));
            $expires_at = time() + (60 * 60 * 24); // 24小时后过期

            // 存储令牌到数据库
            $data = [
                'user_id' => $user_id,
                'token' => $token,
                'expires_at' => date('Y-m-d H:i:s', $expires_at),
                'created_at' => date('Y-m-d H:i:s'),
                'ip_address' => $this->input->ip_address(),
                'user_agent' => $this->input->user_agent()
            ];

            // 先删除该用户的旧令牌
            $this->db->where('user_id', $user_id);
            $this->db->delete('tokens');

            // 插入新令牌
            $this->db->insert('tokens', $data);

            return [
                'token' => $token,
                'expires_in' => $expires_at - time()
            ];

        } catch (Exception $e) {
            log_message('error', '创建令牌错误: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 验证令牌
     *
     * @param string $token 令牌
     * @return array|boolean 成功返回用户信息，失败返回false
     */
    public function validate_token($token) {
        try {
            // 查询令牌
            $this->db->where('token', $token);
            $query = $this->db->get('tokens');
            $token_data = $query->row_array();

            if (!$token_data) {
                return false;
            }

            // 检查令牌是否过期
            $expires_at = strtotime($token_data['expires_at']);
            if (time() > $expires_at) {
                // 删除过期令牌
                $this->db->where('token', $token);
                $this->db->delete('tokens');
                return false;
            }

            // 获取用户信息
            $this->db->where('uid', $token_data['user_id']);
            $this->db->where('isDelete', 0);
            $query = $this->db->get('admin');
            $user = $query->row_array();

            if (!$user || $user['status'] != 1) {
                return false;
            }

            return $user;

        } catch (Exception $e) {
            log_message('error', '验证令牌错误: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 撤销令牌（退出登录）
     *
     * @param string $token 令牌
     */
    public function revoke_token($token) {
        try {
            $this->db->where('token', $token);
            $this->db->delete('tokens');
        } catch (Exception $e) {
            log_message('error', '撤销令牌错误: ' . $e->getMessage());
        }
    }

    /**
     * 刷新令牌
     *
     * @param string $token 旧令牌
     * @param int $user_id 用户ID
     * @return array|boolean 成功返回新令牌数据，失败返回false
     */
    public function refresh_token($token, $user_id) {
        try {
            // 先验证旧令牌
            $this->db->where('token', $token);
            $this->db->where('user_id', $user_id);
            $query = $this->db->get('tokens');

            if ($query->num_rows() == 0) {
                return false;
            }

            // 创建新令牌
            return $this->create_token($user_id);

        } catch (Exception $e) {
            log_message('error', '刷新令牌错误: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户信息
     *
     * @param int $user_id 用户ID
     * @return array|boolean 用户信息
     */
    public function get_user_info($user_id) {
        try {
            $this->db->select('uid, username, name, roleid, mobile, email, status, createTime');
            $this->db->where('uid', $user_id);
            $this->db->where('isDelete', 0);
            $query = $this->db->get('admin');

            return $query->row_array();

        } catch (Exception $e) {
            log_message('error', '获取用户信息错误: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 修改密码
     *
     * @param int $user_id 用户ID
     * @param string $old_password 旧密码
     * @param string $new_password 新密码
     * @return array 修改结果
     */
    public function change_password($user_id, $old_password, $new_password) {
        try {
            // 验证旧密码
            $this->db->where('uid', $user_id);
            $this->db->where('isDelete', 0);
            $query = $this->db->get('admin');
            $user = $query->row_array();

            if (!$user) {
                return [
                    'status' => false,
                    'message' => '用户不存在'
                ];
            }

            if ($user['userpwd'] !== md5($old_password)) {
                return [
                    'status' => false,
                    'message' => '旧密码错误'
                ];
            }

            // 更新密码
            $this->db->where('uid', $user_id);
            $this->db->update('admin', [
                'userpwd' => md5($new_password),
                'modifyTime' => date('Y-m-d H:i:s')
            ]);

            // 撤销所有令牌，强制重新登录
            $this->db->where('user_id', $user_id);
            $this->db->delete('tokens');

            return [
                'status' => true,
                'message' => '密码修改成功'
            ];

        } catch (Exception $e) {
            log_message('error', '修改密码错误: ' . $e->getMessage());
            return [
                'status' => false,
                'message' => '修改密码失败'
            ];
        }
    }

    /**
     * 获取用户权限
     *
     * @param int $role_id 角色ID
     * @return array 权限列表
     */
    private function _get_user_permissions($role_id) {
        try {
            if ($role_id == 0) {
                // 超级管理员拥有所有权限
                return ['*'];
            }

            $this->db->select('lever');
            $this->db->where('id', $role_id);
            $query = $this->db->get('role');
            $role = $query->row_array();

            if ($role && !empty($role['lever'])) {
                return explode(',', $role['lever']);
            }

            return [];

        } catch (Exception $e) {
            log_message('error', '获取用户权限错误: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 记录登录日志
     *
     * @param string $username 用户名
     */
    private function _log_login($username) {
        try {
            $data = [
                'name' => $username,
                'operation' => '登录',
                'content' => '用户登录成功',
                'ip' => $this->input->ip_address(),
                'create_time' => date('Y-m-d H:i:s')
            ];

            $this->db->insert('log', $data);

        } catch (Exception $e) {
            log_message('error', '记录登录日志错误: ' . $e->getMessage());
        }
    }
}
