<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once APPPATH . 'controllers/Api.php';

/**
 * 产品管理控制器
 * 实现产品相关的API接口
 */
class Product extends Api {

    public function __construct() {
        parent::__construct();
        $this->load->model('product_model');
        $this->load->model('category_model');
    }

    /**
     * 获取产品列表
     * GET /api/products
     */
    public function index_get() {
        // 获取查询参数
        $params = [
            'page' => $this->get('page', true) ? $this->get('page', true) : 1,
            'limit' => $this->get('limit', true) ? $this->get('limit', true) : 10,
            'keyword' => $this->get('keyword', true),
            'category_id' => $this->get('category_id', true),
            'status' => $this->get('status', true)
        ];

        // 获取产品列表和总数
        $products = $this->product_model->get_products($params);
        $total = $this->product_model->count_products($params);

        // 返回数据
        $this->response([
            'status' => true,
            'data' => [
                'list' => $products,
                'total' => $total
            ]
        ], 200);
    }

    /**
     * 获取单个产品详情
     * GET /api/products/:id
     */
    public function detail_get($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '产品ID不能为空'
            ], 400);
            return;
        }

        $product = $this->product_model->get_product_by_id($id);
        if (!$product) {
            $this->response([
                'status' => false,
                'message' => '产品不存在'
            ], 404);
            return;
        }

        $this->response([
            'status' => true,
            'data' => $product
        ], 200);
    }

    /**
     * 创建产品
     * POST /api/products
     */
    public function create_post() {
        // 获取请求数据
        $data = [
            'name' => $this->post('name', true),
            'code' => $this->post('code', true),
            'category_id' => $this->post('category_id', true),
            'specification' => $this->post('specification', true),
            'unit' => $this->post('unit', true),
            'purchase_price' => $this->post('purchase_price', true),
            'selling_price' => $this->post('selling_price', true),
            'tax_rate' => $this->post('tax_rate', true),
            'stock_warning' => $this->post('stock_warning', true),
            'status' => $this->post('status', true) ? $this->post('status', true) : 'normal',
            'remark' => $this->post('remark', true),
            'created_at' => date('Y-m-d H:i:s')
        ];

        // 验证必填字段
        if (!$data['name'] || !$data['code']) {
            $this->response([
                'status' => false,
                'message' => '产品名称和编码不能为空'
            ], 400);
            return;
        }

        // 验证产品编码唯一性
        if ($this->product_model->is_code_exists($data['code'])) {
            $this->response([
                'status' => false,
                'message' => '产品编码已存在'
            ], 400);
            return;
        }

        // 创建产品
        $product_id = $this->product_model->create_product($data);
        if (!$product_id) {
            $this->response([
                'status' => false,
                'message' => '创建产品失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '创建产品成功',
            'data' => [
                'id' => $product_id
            ]
        ], 201);
    }

    /**
     * 更新产品
     * PUT /api/products/:id
     */
    public function update_put($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '产品ID不能为空'
            ], 400);
            return;
        }

        // 检查产品是否存在
        $product = $this->product_model->get_product_by_id($id);
        if (!$product) {
            $this->response([
                'status' => false,
                'message' => '产品不存在'
            ], 404);
            return;
        }

        // 获取请求数据
        $data = [
            'name' => $this->put('name', true),
            'category_id' => $this->put('category_id', true),
            'specification' => $this->put('specification', true),
            'unit' => $this->put('unit', true),
            'purchase_price' => $this->put('purchase_price', true),
            'selling_price' => $this->put('selling_price', true),
            'tax_rate' => $this->put('tax_rate', true),
            'stock_warning' => $this->put('stock_warning', true),
            'status' => $this->put('status', true),
            'remark' => $this->put('remark', true),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 更新产品
        $result = $this->product_model->update_product($id, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '更新产品失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '更新产品成功'
        ], 200);
    }

    /**
     * 批量更新产品
     * POST /api/products/batch-update
     */
    public function batch_update_post() {
        // 获取请求数据
        $product_ids = $this->post('product_ids', true);
        $data = [
            'category_id' => $this->post('category_id', true),
            'specification' => $this->post('specification', true),
            'unit' => $this->post('unit', true),
            'purchase_price' => $this->post('purchase_price', true),
            'selling_price' => $this->post('selling_price', true),
            'tax_rate' => $this->post('tax_rate', true),
            'stock_warning' => $this->post('stock_warning', true),
            'status' => $this->post('status', true),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // 验证产品ID
        if (!$product_ids || !is_array($product_ids) || count($product_ids) == 0) {
            $this->response([
                'status' => false,
                'message' => '产品ID列表不能为空'
            ], 400);
            return;
        }

        // 过滤空值
        $data = array_filter($data, function($value) {
            return $value !== null;
        });

        // 验证更新数据
        if (count($data) <= 1) { // 只有updated_at字段
            $this->response([
                'status' => false,
                'message' => '更新字段不能为空'
            ], 400);
            return;
        }

        // 批量更新产品
        $result = $this->product_model->batch_update_products($product_ids, $data);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '批量更新产品失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '批量更新产品成功',
            'data' => [
                'updated_count' => $result
            ]
        ], 200);
    }

    /**
     * 删除产品
     * DELETE /api/products/:id
     */
    public function delete_delete($id) {
        if (!$id) {
            $this->response([
                'status' => false,
                'message' => '产品ID不能为空'
            ], 400);
            return;
        }

        // 检查产品是否存在
        $product = $this->product_model->get_product_by_id($id);
        if (!$product) {
            $this->response([
                'status' => false,
                'message' => '产品不存在'
            ], 404);
            return;
        }

        // 删除产品（软删除）
        $result = $this->product_model->delete_product($id);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '删除产品失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '删除产品成功'
        ], 200);
    }

    /**
     * 批量删除产品
     * POST /api/products/batch-delete
     */
    public function batch_delete_post() {
        // 获取请求数据
        $product_ids = $this->post('ids', true);

        // 验证产品ID
        if (!$product_ids || !is_array($product_ids) || count($product_ids) == 0) {
            $this->response([
                'status' => false,
                'message' => '产品ID列表不能为空'
            ], 400);
            return;
        }

        // 批量删除产品（软删除）
        $result = $this->product_model->batch_delete_products($product_ids);
        if (!$result) {
            $this->response([
                'status' => false,
                'message' => '批量删除产品失败'
            ], 500);
            return;
        }

        $this->response([
            'status' => true,
            'message' => '批量删除产品成功',
            'data' => [
                'deleted_count' => $result
            ]
        ], 200);
    }

    /**
     * 获取产品分类列表
     * GET /api/product-categories
     */
    public function categories_get() {
        $categories = $this->category_model->get_categories('product');
        
        $this->response([
            'status' => true,
            'data' => $categories
        ], 200);
    }
}
