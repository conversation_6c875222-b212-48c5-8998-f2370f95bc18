<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 打印模型类
 * 处理打印模板相关的数据库操作
 */
class Print_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取打印模板列表
     * @param string $type 模板类型
     * @return array 模板列表
     */
    public function get_templates($type = null) {
        $this->db->select('p.*, u.name as created_by_name');
        $this->db->from('print_templates p');
        $this->db->join('users u', 'p.created_by = u.id', 'left');
        $this->db->where('p.is_deleted', 0);
        
        if ($type) {
            $this->db->where('p.type', $type);
        }
        
        $this->db->order_by('p.type', 'ASC');
        $this->db->order_by('p.is_default', 'DESC');
        $this->db->order_by('p.name', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 根据ID获取打印模板
     * @param int $id 模板ID
     * @return array|null 模板详情
     */
    public function get_template_by_id($id) {
        $this->db->select('p.*, u.name as created_by_name, u2.name as updated_by_name');
        $this->db->from('print_templates p');
        $this->db->join('users u', 'p.created_by = u.id', 'left');
        $this->db->join('users u2', 'p.updated_by = u2.id', 'left');
        $this->db->where('p.id', $id);
        $this->db->where('p.is_deleted', 0);
        
        $query = $this->db->get();
        return $query->row_array();
    }
    
    /**
     * 获取默认打印模板
     * @param string $type 模板类型
     * @return array|null 默认模板
     */
    public function get_default_template($type) {
        $this->db->select('*');
        $this->db->from('print_templates');
        $this->db->where('type', $type);
        $this->db->where('is_default', 1);
        $this->db->where('is_deleted', 0);
        
        $query = $this->db->get();
        $template = $query->row_array();
        
        // 如果没有默认模板，返回该类型的第一个模板
        if (!$template) {
            $this->db->select('*');
            $this->db->from('print_templates');
            $this->db->where('type', $type);
            $this->db->where('is_deleted', 0);
            $this->db->order_by('id', 'ASC');
            $this->db->limit(1);
            
            $query = $this->db->get();
            $template = $query->row_array();
        }
        
        return $template;
    }
    
    /**
     * 创建打印模板
     * @param array $data 模板数据
     * @return int|bool 模板ID或false
     */
    public function create_template($data) {
        // 如果设置为默认模板，先将同类型的其他模板设为非默认
        if ($data['is_default']) {
            $this->db->where('type', $data['type']);
            $this->db->update('print_templates', ['is_default' => 0]);
        }
        
        // 插入新模板
        $this->db->insert('print_templates', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 更新打印模板
     * @param int $id 模板ID
     * @param array $data 模板数据
     * @return bool 是否成功
     */
    public function update_template($id, $data) {
        // 如果设置为默认模板，先将同类型的其他模板设为非默认
        if ($data['is_default']) {
            $this->db->where('type', $data['type']);
            $this->db->where('id !=', $id);
            $this->db->update('print_templates', ['is_default' => 0]);
        }
        
        // 更新模板
        $this->db->where('id', $id);
        return $this->db->update('print_templates', $data);
    }
    
    /**
     * 删除打印模板（软删除）
     * @param int $id 模板ID
     * @return bool 是否成功
     */
    public function delete_template($id) {
        // 检查是否为默认模板
        $template = $this->get_template_by_id($id);
        if ($template && $template['is_default']) {
            // 如果是默认模板，找到同类型的另一个模板设为默认
            $this->db->select('id');
            $this->db->from('print_templates');
            $this->db->where('type', $template['type']);
            $this->db->where('id !=', $id);
            $this->db->where('is_deleted', 0);
            $this->db->order_by('id', 'ASC');
            $this->db->limit(1);
            
            $query = $this->db->get();
            $new_default = $query->row_array();
            
            if ($new_default) {
                $this->db->where('id', $new_default['id']);
                $this->db->update('print_templates', ['is_default' => 1]);
            }
        }
        
        // 软删除模板
        $this->db->where('id', $id);
        return $this->db->update('print_templates', [
            'is_deleted' => 1,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 设置默认打印模板
     * @param int $id 模板ID
     * @param string $type 模板类型
     * @return bool 是否成功
     */
    public function set_default_template($id, $type) {
        // 开启事务
        $this->db->trans_start();
        
        // 将同类型的所有模板设为非默认
        $this->db->where('type', $type);
        $this->db->update('print_templates', ['is_default' => 0]);
        
        // 将指定模板设为默认
        $this->db->where('id', $id);
        $this->db->update('print_templates', ['is_default' => 1]);
        
        // 提交事务
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 获取打印模板变量列表
     * @param string $type 模板类型
     * @return array 变量列表
     */
    public function get_template_variables($type) {
        // 公共变量
        $common_variables = [
            'company.name' => '公司名称',
            'company.address' => '公司地址',
            'company.phone' => '公司电话',
            'company.email' => '公司邮箱',
            'company.website' => '公司网站',
            'company.tax_number' => '公司税号',
            'document.title' => '单据标题',
            'document.id' => '单据ID',
            'document.code' => '单据编号',
            'document.date' => '单据日期',
            'document.create_time' => '创建时间',
            'document.total_amount' => '合计金额（未税）',
            'document.discount_amount' => '折扣金额',
            'document.tax_amount' => '税额',
            'document.amount' => '价税合计',
            'document.created_by' => '经办人',
            'document.remark' => '备注'
        ];
        
        // 特定类型的变量
        $type_variables = [];
        
        switch ($type) {
            case 'sale':
                $type_variables = [
                    'customer.name' => '客户名称',
                    'customer.contact' => '联系人',
                    'customer.phone' => '联系电话',
                    'customer.address' => '客户地址'
                ];
                break;
                
            case 'purchase':
                $type_variables = [
                    'supplier.name' => '供应商名称',
                    'supplier.contact' => '联系人',
                    'supplier.phone' => '联系电话',
                    'supplier.address' => '供应商地址'
                ];
                break;
                
            case 'inventory':
                $type_variables = [
                    'warehouse.name' => '仓库名称',
                    'inventory.type' => '库存单据类型'
                ];
                break;
        }
        
        return array_merge($common_variables, $type_variables);
    }
    
    /**
     * 获取模板默认内容
     * @param string $type 模板类型
     * @return string 默认模板内容
     */
    public function get_default_template_content($type) {
        // 根据类型返回默认模板内容
        switch ($type) {
            case 'sale':
                return $this->get_default_sale_template();
                
            case 'purchase':
                return $this->get_default_purchase_template();
                
            case 'inventory':
                return $this->get_default_inventory_template();
                
            default:
                return '';
        }
    }
    
    /**
     * 获取默认销售单模板
     * @return string 模板内容
     */
    private function get_default_sale_template() {
        $html = '
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 10px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 5px;
                text-align: center;
            }
            th {
                background-color: #f2f2f2;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            .title {
                font-size: 18px;
                font-weight: bold;
            }
            .info {
                margin-bottom: 10px;
            }
            .info td {
                border: none;
                text-align: left;
                padding: 2px 5px;
            }
            .footer {
                margin-top: 20px;
            }
            .footer td {
                border: none;
                text-align: left;
            }
        </style>
        
        <div class="header">
            <div class="title">{{company.name}}</div>
            <div class="title">{{document.title}}</div>
        </div>
        
        <table class="info">
            <tr>
                <td width="50%">单据编号：{{document.code}}</td>
                <td width="50%">日期：{{document.date}}</td>
            </tr>
            <tr>
                <td>客户名称：{{customer.name}}</td>
                <td>联系人：{{customer.contact}}</td>
            </tr>
            <tr>
                <td>联系电话：{{customer.phone}}</td>
                <td>地址：{{customer.address}}</td>
            </tr>
        </table>
        
        <table>
            <thead>
                <tr>
                    <th width="5%">序号</th>
                    <th width="10%">产品编码</th>
                    <th width="20%">产品名称</th>
                    <th width="15%">规格型号</th>
                    <th width="5%">单位</th>
                    <th width="10%">数量</th>
                    <th width="10%">单价</th>
                    <th width="10%">金额</th>
                    <th width="15%">备注</th>
                </tr>
            </thead>
            <tbody>
                {{table.rows}}
            </tbody>
        </table>
        
        <table class="info">
            <tr>
                <td width="50%">合计金额：{{document.total_amount}}</td>
                <td width="50%">折扣金额：{{document.discount_amount}}</td>
            </tr>
            <tr>
                <td>税额：{{document.tax_amount}}</td>
                <td>价税合计：{{document.amount}}</td>
            </tr>
            <tr>
                <td colspan="2">备注：{{document.remark}}</td>
            </tr>
        </table>
        
        <table class="footer">
            <tr>
                <td width="33%">制单人：{{document.created_by}}</td>
                <td width="33%">审核人：_____________</td>
                <td width="33%">客户签字：_____________</td>
            </tr>
        </table>';
        
        return $html;
    }
    
    /**
     * 获取默认采购单模板
     * @return string 模板内容
     */
    private function get_default_purchase_template() {
        $html = '
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 10px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 5px;
                text-align: center;
            }
            th {
                background-color: #f2f2f2;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            .title {
                font-size: 18px;
                font-weight: bold;
            }
            .info {
                margin-bottom: 10px;
            }
            .info td {
                border: none;
                text-align: left;
                padding: 2px 5px;
            }
            .footer {
                margin-top: 20px;
            }
            .footer td {
                border: none;
                text-align: left;
            }
        </style>
        
        <div class="header">
            <div class="title">{{company.name}}</div>
            <div class="title">{{document.title}}</div>
        </div>
        
        <table class="info">
            <tr>
                <td width="50%">单据编号：{{document.code}}</td>
                <td width="50%">日期：{{document.date}}</td>
            </tr>
            <tr>
                <td>供应商名称：{{supplier.name}}</td>
                <td>联系人：{{supplier.contact}}</td>
            </tr>
            <tr>
                <td>联系电话：{{supplier.phone}}</td>
                <td>地址：{{supplier.address}}</td>
            </tr>
        </table>
        
        <table>
            <thead>
                <tr>
                    <th width="5%">序号</th>
                    <th width="10%">产品编码</th>
                    <th width="20%">产品名称</th>
                    <th width="15%">规格型号</th>
                    <th width="5%">单位</th>
                    <th width="10%">数量</th>
                    <th width="10%">单价</th>
                    <th width="10%">金额</th>
                    <th width="15%">备注</th>
                </tr>
            </thead>
            <tbody>
                {{table.rows}}
            </tbody>
        </table>
        
        <table class="info">
            <tr>
                <td width="50%">合计金额：{{document.total_amount}}</td>
                <td width="50%">折扣金额：{{document.discount_amount}}</td>
            </tr>
            <tr>
                <td>税额：{{document.tax_amount}}</td>
                <td>价税合计：{{document.amount}}</td>
            </tr>
            <tr>
                <td colspan="2">备注：{{document.remark}}</td>
            </tr>
        </table>
        
        <table class="footer">
            <tr>
                <td width="33%">制单人：{{document.created_by}}</td>
                <td width="33%">审核人：_____________</td>
                <td width="33%">供应商签字：_____________</td>
            </tr>
        </table>';
        
        return $html;
    }
    
    /**
     * 获取默认库存单模板
     * @return string 模板内容
     */
    private function get_default_inventory_template() {
        $html = '
        <style>
            table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 10px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 5px;
                text-align: center;
            }
            th {
                background-color: #f2f2f2;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            .title {
                font-size: 18px;
                font-weight: bold;
            }
            .info {
                margin-bottom: 10px;
            }
            .info td {
                border: none;
                text-align: left;
                padding: 2px 5px;
            }
            .footer {
                margin-top: 20px;
            }
            .footer td {
                border: none;
                text-align: left;
            }
        </style>
        
        <div class="header">
            <div class="title">{{company.name}}</div>
            <div class="title">{{document.title}} - {{inventory.type}}</div>
        </div>
        
        <table class="info">
            <tr>
                <td width="50%">单据编号：{{document.code}}</td>
                <td width="50%">日期：{{document.date}}</td>
            </tr>
            <tr>
                <td>仓库名称：{{warehouse.name}}</td>
                <td>单据类型：{{inventory.type}}</td>
            </tr>
        </table>
        
        <table>
            <thead>
                <tr>
                    <th width="5%">序号</th>
                    <th width="10%">产品编码</th>
                    <th width="20%">产品名称</th>
                    <th width="15%">规格型号</th>
                    <th width="5%">单位</th>
                    <th width="10%">数量</th>
                    <th width="10%">单价</th>
                    <th width="10%">金额</th>
                    <th width="15%">备注</th>
                </tr>
            </thead>
            <tbody>
                {{table.rows}}
            </tbody>
        </table>
        
        <table class="info">
            <tr>
                <td colspan="2">备注：{{document.remark}}</td>
            </tr>
        </table>
        
        <table class="footer">
            <tr>
                <td width="33%">制单人：{{document.created_by}}</td>
                <td width="33%">审核人：_____________</td>
                <td width="33%">仓库管理员：_____________</td>
            </tr>
        </table>';
        
        return $html;
    }
}
