<?php
/**
 * 云进销存系统 - API入口文件
 * 
 * 前后端分离架构，这是API后端的入口点
 */

// 定义应用环境
define('ENVIRONMENT', isset($_SERVER['CI_ENV']) ? $_SERVER['CI_ENV'] : 'development');

// 错误报告设置
switch (ENVIRONMENT) {
    case 'development':
        error_reporting(-1);
        ini_set('display_errors', 1);
        break;
    case 'testing':
    case 'production':
        ini_set('display_errors', 0);
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT & ~E_USER_NOTICE & ~E_USER_DEPRECATED);
        break;
    default:
        header('HTTP/1.1 503 Service Unavailable.', TRUE, 503);
        echo 'The application environment is not set correctly.';
        exit(1);
}

// 设置系统路径常量
$system_path = 'system';
$application_folder = 'application';
$view_folder = '';

// 设置系统目录路径
if (($_temp = realpath($system_path)) !== FALSE) {
    $system_path = $_temp.'/';
} else {
    $system_path = rtrim($system_path, '/').'/'; 
}

// 确保系统路径包含尾部斜杠
$system_path = rtrim($system_path, '/').'/'; 

// 检测系统目录是否正确
if (!is_dir($system_path)) {
    header('HTTP/1.1 503 Service Unavailable.', TRUE, 503);
    echo 'Your system folder path does not appear to be set correctly. Please open the following file and correct this: '.pathinfo(__FILE__, PATHINFO_BASENAME);
    exit(3); 
}

// 定义其他路径常量
define('SELF', pathinfo(__FILE__, PATHINFO_BASENAME));
define('BASEPATH', $system_path);
define('FCPATH', dirname(__FILE__).'/');
define('SYSDIR', trim(strrchr(trim(BASEPATH, '/'), '/'), '/'));

// 设置应用目录路径
if (is_dir($application_folder)) {
    if (($_temp = realpath($application_folder)) !== FALSE) {
        $application_folder = $_temp;
    }

    define('APPPATH', $application_folder.DIRECTORY_SEPARATOR);
} else {
    if (!is_dir(BASEPATH.$application_folder.DIRECTORY_SEPARATOR)) {
        header('HTTP/1.1 503 Service Unavailable.', TRUE, 503);
        echo 'Your application folder path does not appear to be set correctly. Please open the following file and correct this: '.SELF;
        exit(3);
    }

    define('APPPATH', BASEPATH.$application_folder.DIRECTORY_SEPARATOR);
}

// 设置视图目录路径
if (!empty($view_folder)) {
    if (($_temp = realpath($view_folder)) !== FALSE) {
        $view_folder = $_temp;
    } elseif (!is_dir(APPPATH.'views'.DIRECTORY_SEPARATOR)) {
        header('HTTP/1.1 503 Service Unavailable.', TRUE, 503);
        echo 'Your view folder path does not appear to be set correctly. Please open the following file and correct this: '.SELF;
        exit(3);
    } else {
        $view_folder = APPPATH.'views';
    }

    define('VIEWPATH', $view_folder.DIRECTORY_SEPARATOR);
} else {
    define('VIEWPATH', APPPATH.'views'.DIRECTORY_SEPARATOR);
}

// 添加REST API支持的头部
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// 处理OPTIONS请求（预检请求）
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 加载CodeIgniter框架核心文件
require_once BASEPATH.'core/CodeIgniter.php';
