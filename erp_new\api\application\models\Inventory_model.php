<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * 库存模型类
 * 处理库存相关的数据库操作
 */
class Inventory_model extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * 获取库存调拨记录列表
     * @param array $params 查询参数
     * @return array 库存调拨记录列表
     */
    public function get_transfers($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('t.*, sw.name as source_warehouse_name, tw.name as target_warehouse_name, u.name as creator_name');
        $this->db->from('inventory_transfers t');
        $this->db->join('warehouses sw', 't.source_warehouse_id = sw.id', 'left');
        $this->db->join('warehouses tw', 't.target_warehouse_id = tw.id', 'left');
        $this->db->join('users u', 't.created_by = u.id', 'left');
        $this->db->where('t.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('t.transfer_no', $keyword);
            $this->db->or_like('sw.name', $keyword);
            $this->db->or_like('tw.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('t.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('t.transfer_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('t.transfer_date <=', $params['end_date']);
        }
        
        if (isset($params['source_warehouse_id']) && $params['source_warehouse_id']) {
            $this->db->where('t.source_warehouse_id', $params['source_warehouse_id']);
        }
        
        if (isset($params['target_warehouse_id']) && $params['target_warehouse_id']) {
            $this->db->where('t.target_warehouse_id', $params['target_warehouse_id']);
        }
        
        $this->db->order_by('t.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取库存调拨记录总数
     * @param array $params 查询参数
     * @return int 库存调拨记录总数
     */
    public function count_transfers($params = []) {
        $this->db->from('inventory_transfers t');
        $this->db->join('warehouses sw', 't.source_warehouse_id = sw.id', 'left');
        $this->db->join('warehouses tw', 't.target_warehouse_id = tw.id', 'left');
        $this->db->where('t.is_deleted', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('t.transfer_no', $keyword);
            $this->db->or_like('sw.name', $keyword);
            $this->db->or_like('tw.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('t.status', $params['status']);
        }
        
        if (isset($params['start_date']) && $params['start_date']) {
            $this->db->where('t.transfer_date >=', $params['start_date']);
        }
        
        if (isset($params['end_date']) && $params['end_date']) {
            $this->db->where('t.transfer_date <=', $params['end_date']);
        }
        
        if (isset($params['source_warehouse_id']) && $params['source_warehouse_id']) {
            $this->db->where('t.source_warehouse_id', $params['source_warehouse_id']);
        }
        
        if (isset($params['target_warehouse_id']) && $params['target_warehouse_id']) {
            $this->db->where('t.target_warehouse_id', $params['target_warehouse_id']);
        }
        
        return $this->db->count_all_results();
    }
    
    /**
     * 获取库存调拨记录详情
     * @param int $id 库存调拨记录ID
     * @return array|null 库存调拨记录详情
     */
    public function get_transfer_by_id($id) {
        $this->db->select('t.*, sw.name as source_warehouse_name, tw.name as target_warehouse_name, u.name as creator_name');
        $this->db->from('inventory_transfers t');
        $this->db->join('warehouses sw', 't.source_warehouse_id = sw.id', 'left');
        $this->db->join('warehouses tw', 't.target_warehouse_id = tw.id', 'left');
        $this->db->join('users u', 't.created_by = u.id', 'left');
        $this->db->where('t.id', $id);
        $this->db->where('t.is_deleted', 0);
        
        $query = $this->db->get();
        $transfer = $query->row_array();
        
        if ($transfer) {
            // 获取调拨单详情项
            $this->db->select('td.*, p.name as product_name, p.code as product_code, p.specification');
            $this->db->from('inventory_transfer_details td');
            $this->db->join('products p', 'td.product_id = p.id', 'left');
            $this->db->where('td.transfer_id', $id);
            
            $query = $this->db->get();
            $transfer['details'] = $query->result_array();
        }
        
        return $transfer;
    }
    
    /**
     * 创建库存调拨记录
     * @param array $data 库存调拨记录数据
     * @param array $details 库存调拨记录详情数据
     * @return int|bool 库存调拨记录ID或false
     */
    public function create_transfer($data, $details) {
        $this->db->trans_start();
        
        // 插入库存调拨记录
        $this->db->insert('inventory_transfers', $data);
        $transfer_id = $this->db->insert_id();
        
        // 插入库存调拨记录详情
        if ($transfer_id && is_array($details) && count($details) > 0) {
            foreach ($details as $detail) {
                $detail['transfer_id'] = $transfer_id;
                $this->db->insert('inventory_transfer_details', $detail);
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : $transfer_id;
    }
    
    /**
     * 更新库存调拨记录
     * @param int $id 库存调拨记录ID
     * @param array $data 库存调拨记录数据
     * @param array $details 库存调拨记录详情数据
     * @return bool 是否成功
     */
    public function update_transfer($id, $data, $details = null) {
        $this->db->trans_start();
        
        // 更新库存调拨记录
        $this->db->where('id', $id);
        $this->db->update('inventory_transfers', $data);
        
        // 如果提供了详情数据，则更新详情
        if ($details !== null) {
            // 删除原有的详情
            $this->db->where('transfer_id', $id);
            $this->db->delete('inventory_transfer_details');
            
            // 插入新的详情
            if (is_array($details) && count($details) > 0) {
                foreach ($details as $detail) {
                    $detail['transfer_id'] = $id;
                    $this->db->insert('inventory_transfer_details', $detail);
                }
            }
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 更新库存调拨记录状态
     * @param int $id 库存调拨记录ID
     * @param string $status 状态
     * @param int $user_id 操作用户ID
     * @return bool 是否成功
     */
    public function update_transfer_status($id, $status, $user_id) {
        $data = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($status == 'submitted') {
            $data['submitted_by'] = $user_id;
            $data['submitted_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'approved') {
            $data['approved_by'] = $user_id;
            $data['approved_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'completed') {
            $data['completed_by'] = $user_id;
            $data['completed_at'] = date('Y-m-d H:i:s');
        } else if ($status == 'cancelled') {
            $data['cancelled_by'] = $user_id;
            $data['cancelled_at'] = date('Y-m-d H:i:s');
        }
        
        $this->db->where('id', $id);
        return $this->db->update('inventory_transfers', $data);
    }
    
    /**
     * 删除库存调拨记录
     * @param int $id 库存调拨记录ID
     * @return bool 是否成功
     */
    public function delete_transfer($id) {
        $this->db->where('id', $id);
        return $this->db->update('inventory_transfers', ['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 获取仓库列表
     * @return array 仓库列表
     */
    public function get_warehouses() {
        $this->db->from('warehouses');
        $this->db->where('is_deleted', 0);
        $this->db->order_by('name', 'ASC');
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取产品当前库存
     * @param int $product_id 产品ID
     * @param int $warehouse_id 仓库ID
     * @return int 库存数量
     */
    public function get_product_stock($product_id, $warehouse_id) {
        $this->db->select('quantity');
        $this->db->from('stock');
        $this->db->where('product_id', $product_id);
        $this->db->where('warehouse_id', $warehouse_id);
        
        $query = $this->db->get();
        $result = $query->row_array();
        
        return $result ? $result['quantity'] : 0;
    }
    
    /**
     * 更新产品库存
     * @param int $product_id 产品ID
     * @param int $warehouse_id 仓库ID
     * @param int $quantity 变动数量
     * @return bool 是否成功
     */
    public function update_stock($product_id, $warehouse_id, $quantity) {
        $this->db->select('id, quantity');
        $this->db->from('stock');
        $this->db->where('product_id', $product_id);
        $this->db->where('warehouse_id', $warehouse_id);
        
        $query = $this->db->get();
        $stock = $query->row_array();
        
        if ($stock) {
            // 更新库存记录
            $new_quantity = $stock['quantity'] + $quantity;
            $this->db->where('id', $stock['id']);
            return $this->db->update('stock', ['quantity' => $new_quantity, 'updated_at' => date('Y-m-d H:i:s')]);
        } else {
            // 创建新的库存记录
            $data = [
                'product_id' => $product_id,
                'warehouse_id' => $warehouse_id,
                'quantity' => $quantity,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            return $this->db->insert('stock', $data);
        }
    }
    
    /**
     * 完成库存调拨（调整库存）
     * @param int $id 库存调拨记录ID
     * @return bool 是否成功
     */
    public function complete_transfer($id) {
        $transfer = $this->get_transfer_by_id($id);
        
        if (!$transfer || $transfer['status'] != 'approved') {
            return false;
        }
        
        $this->db->trans_start();
        
        foreach ($transfer['details'] as $detail) {
            // 源仓库减少库存
            $this->update_stock($detail['product_id'], $transfer['source_warehouse_id'], -$detail['quantity']);
            
            // 目标仓库增加库存
            $this->update_stock($detail['product_id'], $transfer['target_warehouse_id'], $detail['quantity']);
            
            // 添加库存流水记录
            $this->add_stock_movement([
                'product_id' => $detail['product_id'],
                'warehouse_id' => $transfer['source_warehouse_id'],
                'quantity' => -$detail['quantity'],
                'reference_type' => 'transfer',
                'reference_id' => $id,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $this->add_stock_movement([
                'product_id' => $detail['product_id'],
                'warehouse_id' => $transfer['target_warehouse_id'],
                'quantity' => $detail['quantity'],
                'reference_type' => 'transfer',
                'reference_id' => $id,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        }
        
        $this->db->trans_complete();
        
        return ($this->db->trans_status() === FALSE) ? FALSE : TRUE;
    }
    
    /**
     * 添加库存流水记录
     * @param array $data 库存流水数据
     * @return int|bool 库存流水ID或false
     */
    public function add_stock_movement($data) {
        $this->db->insert('stock_movements', $data);
        return $this->db->insert_id();
    }
    
    /**
     * 检查产品是否有库存记录
     * @param int $product_id 产品ID
     * @return bool 是否有库存记录
     */
    public function check_product_has_inventory($product_id) {
        $this->db->from('stock');
        $this->db->where('product_id', $product_id);
        $this->db->where('quantity >', 0);
        
        return $this->db->count_all_results() > 0;
    }
    
    /**
     * 获取库存列表
     * @param array $params 查询参数
     * @return array 库存列表
     */
    public function get_stock_list($params = []) {
        $page = isset($params['page']) ? $params['page'] : 1;
        $limit = isset($params['limit']) ? $params['limit'] : 10;
        $offset = ($page - 1) * $limit;
        
        $this->db->select('s.id, s.product_id, p.code as product_code, p.name as product_name, '
                . 's.warehouse_id, w.name as warehouse_name, s.quantity, p.unit, p.status');
        $this->db->from('stock s');
        $this->db->join('products p', 's.product_id = p.id', 'left');
        $this->db->join('warehouses w', 's.warehouse_id = w.id', 'left');
        $this->db->where('p.is_deleted', 0);
        $this->db->where('s.quantity >', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('p.name', $keyword);
            $this->db->or_like('p.code', $keyword);
            $this->db->or_like('w.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['warehouse_id']) && $params['warehouse_id']) {
            $this->db->where('s.warehouse_id', $params['warehouse_id']);
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('p.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('p.status', $params['status']);
        }
        
        $this->db->order_by('s.id', 'DESC');
        $this->db->limit($limit, $offset);
        
        $query = $this->db->get();
        return $query->result_array();
    }
    
    /**
     * 获取库存总数
     * @param array $params 查询参数
     * @return int 库存总数
     */
    public function count_stock_list($params = []) {
        $this->db->from('stock s');
        $this->db->join('products p', 's.product_id = p.id', 'left');
        $this->db->join('warehouses w', 's.warehouse_id = w.id', 'left');
        $this->db->where('p.is_deleted', 0);
        $this->db->where('s.quantity >', 0);
        
        // 搜索条件
        if (isset($params['keyword']) && $params['keyword']) {
            $keyword = $params['keyword'];
            $this->db->group_start();
            $this->db->like('p.name', $keyword);
            $this->db->or_like('p.code', $keyword);
            $this->db->or_like('w.name', $keyword);
            $this->db->group_end();
        }
        
        if (isset($params['warehouse_id']) && $params['warehouse_id']) {
            $this->db->where('s.warehouse_id', $params['warehouse_id']);
        }
        
        if (isset($params['category_id']) && $params['category_id']) {
            $this->db->where('p.category_id', $params['category_id']);
        }
        
        if (isset($params['status']) && $params['status']) {
            $this->db->where('p.status', $params['status']);
        }
        
        return $this->db->count_all_results();
    }
}
